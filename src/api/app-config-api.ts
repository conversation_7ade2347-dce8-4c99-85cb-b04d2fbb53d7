import { doc, getDoc, setDoc } from 'firebase/firestore';

import { CACHE_CONFIG, CachePatterns } from '@/core.constants';
import type { AppConfigEntity } from '@/marketplace-shared';
import { APP_CONFIG_COLLECTION, APP_CONFIG_DOC_ID } from '@/marketplace-shared';
import { firestore } from '@/root-context';
import { globalCache } from '@/utils/cache-utils';

export const getAppConfig = async (): Promise<AppConfigEntity | null> => {
  const cachedConfig = globalCache.getAppConfig<AppConfigEntity>();
  if (cachedConfig) {
    return cachedConfig;
  }

  try {
    const configRef = doc(firestore, APP_CONFIG_COLLECTION, APP_CONFIG_DOC_ID);
    const configDoc = await getDoc(configRef);

    if (!configDoc.exists()) {
      return null;
    }

    const config = {
      id: configDoc.id,
      ...configDoc.data(),
    } as AppConfigEntity;

    globalCache.setAppConfig(config, CACHE_CONFIG);

    return config;
  } catch (error) {
    console.error('Error getting app config:', error);
    throw error;
  }
};

export const updateAppConfig = async (updates: Partial<AppConfigEntity>) => {
  try {
    const configRef = doc(firestore, APP_CONFIG_COLLECTION, APP_CONFIG_DOC_ID);

    await setDoc(configRef, updates, { merge: true });

    clearAppConfigCache();

    console.log('App config updated successfully:', updates);
  } catch (error) {
    console.error('Error updating app config:', error);
    throw error;
  }
};

export const clearAppConfigCache = () => {
  globalCache.invalidate(CachePatterns.APP_CONFIG);
};
