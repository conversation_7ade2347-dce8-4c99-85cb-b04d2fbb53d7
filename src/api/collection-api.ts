import type { DocumentSnapshot } from 'firebase/firestore';
import {
  collection,
  deleteDoc,
  doc,
  getDocs,
  limit,
  orderBy,
  query,
  setDoc,
  startAfter,
  updateDoc,
} from 'firebase/firestore';

import { CachePatterns, COLLECTION_CACHE_CONFIG } from '@/core.constants';
import type { CollectionEntity } from '@/marketplace-shared';
import { COLLECTION_NAME } from '@/marketplace-shared';
import { firestore } from '@/root-context';
import { globalCache } from '@/utils/cache-utils';

export const getAllCollections = async () => {
  const cachedCollections = globalCache.getCollections<CollectionEntity>();
  if (cachedCollections) {
    return cachedCollections.sort((a: CollectionEntity, b: CollectionEntity) =>
      a.name.localeCompare(b.name),
    );
  }

  try {
    const snapshot = await getDocs(
      query(collection(firestore, COLLECTION_NAME), orderBy('name', 'asc')),
    );

    const collections: CollectionEntity[] = [];
    snapshot.forEach((doc) => {
      collections.push({ id: doc.id, ...doc.data() } as CollectionEntity);
    });

    globalCache.setCollections(collections, COLLECTION_CACHE_CONFIG);

    return collections;
  } catch (error) {
    console.error('Error fetching all collections:', error);
    throw error;
  }
};

export const getActiveCollections = async () => {
  const cachedCollections = globalCache.getCollections<CollectionEntity>();
  if (cachedCollections) {
    return cachedCollections
      .filter((collection) => collection.active !== false)
      .sort((a: CollectionEntity, b: CollectionEntity) =>
        a.name.localeCompare(b.name),
      );
  }

  try {
    // First get all collections, then filter and sort in memory to avoid index requirement
    const snapshot = await getDocs(
      query(collection(firestore, COLLECTION_NAME)),
    );

    const collections: CollectionEntity[] = [];
    snapshot.forEach((doc) => {
      const data = doc.data() as CollectionEntity;
      // Filter for active collections (active !== false)
      if (data.active !== false) {
        collections.push({ ...data, id: doc.id });
      }
    });

    // Sort by name
    collections.sort((a: CollectionEntity, b: CollectionEntity) =>
      a.name.localeCompare(b.name),
    );

    return collections;
  } catch (error) {
    console.error('Error fetching active collections:', error);
    throw error;
  }
};

export const createCollection = async (collectionData: CollectionEntity) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, collectionData.id);
    await setDoc(docRef, {
      ...collectionData,
    });

    globalCache.invalidate(CachePatterns.COLLECTIONS);

    return collectionData.id;
  } catch (error) {
    console.error('Error creating collection:', error);
    throw error;
  }
};

export const updateCollection = async (
  id: string,
  collectionData: Partial<CollectionEntity>,
) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await updateDoc(docRef, {
      ...collectionData,
    });

    globalCache.invalidate(CachePatterns.COLLECTIONS);
  } catch (error) {
    console.error('Error updating collection:', error);
    throw error;
  }
};

export const deleteCollection = async (id: string) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await deleteDoc(docRef);

    globalCache.invalidate(CachePatterns.COLLECTIONS);
  } catch (error) {
    console.error('Error deleting collection:', error);
    throw error;
  }
};

export const getCollections = async (
  pageSize: number = 10,
  lastDoc?: DocumentSnapshot,
) => {
  try {
    if (!lastDoc && pageSize >= 100) {
      const cachedCollections = globalCache.getCollections<CollectionEntity>();
      if (cachedCollections) {
        const limitedCollections = cachedCollections
          .toSorted((a: CollectionEntity, b: CollectionEntity) =>
            a.name.localeCompare(b.name),
          )
          .slice(0, pageSize);

        return {
          collections: limitedCollections,
          lastDoc: undefined,
          hasMore:
            limitedCollections.length === pageSize &&
            cachedCollections.length > pageSize,
        };
      }
    }

    let q = query(
      collection(firestore, COLLECTION_NAME),
      orderBy('name', 'asc'),
      limit(pageSize),
    );

    if (lastDoc) {
      q = query(
        collection(firestore, COLLECTION_NAME),
        orderBy('name', 'asc'),
        startAfter(lastDoc),
        limit(pageSize),
      );
    }

    const snapshot = await getDocs(q);
    const collections: CollectionEntity[] = [];

    snapshot.forEach((doc) => {
      collections.push({ id: doc.id, ...doc.data() } as CollectionEntity);
    });

    return {
      collections,
      lastDoc: snapshot.docs[snapshot.docs.length - 1],
      hasMore: snapshot.docs.length === pageSize,
    };
  } catch (error) {
    console.error('Error fetching collections:', error);
    throw error;
  }
};

export const clearCollectionsCache = () => {
  globalCache.invalidate(CachePatterns.COLLECTIONS);
};

export const getCollectionsWithPagination = async (
  page: number = 1,
  pageSize: number = 25,
  activeOnly: boolean = false,
) => {
  try {
    const allCollectionsSnapshot = await getDocs(
      query(collection(firestore, COLLECTION_NAME), orderBy('name', 'asc')),
    );

    const allCollections: CollectionEntity[] = [];
    allCollectionsSnapshot.forEach((doc) => {
      const data = doc.data() as CollectionEntity;
      // Apply active filter if requested
      if (!activeOnly || data.active) {
        allCollections.push({ ...data, id: doc.id });
      }
    });

    const totalItems = allCollections.length;
    const offset = (page - 1) * pageSize;
    const collections = allCollections.slice(offset, offset + pageSize);

    return {
      items: collections,
      totalItems,
      hasMore: offset + pageSize < totalItems,
    };
  } catch (error) {
    console.error('Error fetching collections with pagination:', error);
    throw error;
  }
};
