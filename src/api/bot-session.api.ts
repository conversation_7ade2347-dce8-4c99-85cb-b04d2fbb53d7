import { doc, getDoc, setDoc } from 'firebase/firestore';

import type { BotSessionEntity } from '@/marketplace-shared';
import { firestore } from '@/root-context';

const BOT_SESSIONS_COLLECTION = 'bot_sessions';

export interface BotSessionResponse {
  success: boolean;
  session?: BotSessionEntity | null;
  message?: string;
}

export interface SaveBotSessionLanguageParams {
  tgId: string;
  language: string;
}

export const getBotSessionByTgId = async (
  tgId: string,
): Promise<BotSessionResponse> => {
  try {
    if (!tgId) {
      return {
        success: false,
        message: 'Telegram ID is required',
      };
    }

    const sessionRef = doc(firestore, BOT_SESSIONS_COLLECTION, tgId);
    const sessionDoc = await getDoc(sessionRef);

    if (!sessionDoc.exists()) {
      return {
        success: true,
        session: null,
        message: 'No session found',
      };
    }

    const sessionData = {
      id: sessionDoc.id,
      ...sessionDoc.data(),
    } as BotSessionEntity;

    return {
      success: true,
      session: sessionData,
      message: 'Session retrieved successfully',
    };
  } catch (error) {
    console.error('Error getting bot session:', error);
    return {
      success: false,
      message: 'Failed to get bot session',
    };
  }
};

/**
 * Save language preference to bot session
 */
export const saveBotSessionLanguage = async ({
  tgId,
  language,
}: SaveBotSessionLanguageParams): Promise<{
  success: boolean;
  message?: string;
}> => {
  try {
    if (!tgId || !language) {
      return {
        success: false,
        message: 'Telegram ID and language are required',
      };
    }

    const sessionRef = doc(firestore, BOT_SESSIONS_COLLECTION, tgId);

    // If language is English (default), we don't store it to optimize storage
    if (language === 'en') {
      // For English, we could delete the language_preference field
      // but for simplicity in the UI, we'll just not store it
      return {
        success: true,
        message: 'English is default, no storage needed',
      };
    }

    // Store non-English language preferences
    const sessionData: Partial<BotSessionEntity> = {
      id: tgId,
      language_preference: language,
      updatedAt: new Date(),
    };

    await setDoc(sessionRef, sessionData, { merge: true });

    return {
      success: true,
      message: 'Language preference saved successfully',
    };
  } catch (error) {
    console.error('Error saving bot session language:', error);
    return {
      success: false,
      message: 'Failed to save language preference',
    };
  }
};
