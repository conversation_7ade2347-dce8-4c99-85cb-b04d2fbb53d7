import { useIntl } from 'react-intl';

import { TxType } from '@/marketplace-shared';
import { transactionTypeMessages } from '@/utils/intl/transaction-type.messages';

export const useTransactionTypeText = () => {
  const intl = useIntl();

  const transactionTypeMap = new Map([
    [TxType.DEPOSIT, intl.formatMessage(transactionTypeMessages.deposit)],
    [TxType.WITHDRAW, intl.formatMessage(transactionTypeMessages.withdraw)],
    [
      TxType.BUY_LOCK_COLLATERAL,
      intl.formatMessage(transactionTypeMessages.buyLockCollateral),
    ],
    [
      TxType.UNLOCK_COLLATERAL,
      intl.formatMessage(transactionTypeMessages.unlockCollateral),
    ],
    [
      TxType.SELL_LOCK_COLLATERAL,
      intl.formatMessage(transactionTypeMessages.sellLockCollateral),
    ],
    [
      TxType.REFERRAL_FEE,
      intl.formatMessage(transactionTypeMessages.referralFee),
    ],
    [
      TxType.CANCELATION_FEE,
      intl.formatMessage(transactionTypeMessages.cancelationFee),
    ],
    [TxType.REFUND, intl.formatMessage(transactionTypeMessages.refund)],
    [
      TxType.SELL_FULFILLMENT,
      intl.formatMessage(transactionTypeMessages.sellFulfillment),
    ],
    [
      TxType.RESELL_FEE_EARNINGS,
      intl.formatMessage(transactionTypeMessages.resellFeeEarnings),
    ],
  ]);

  return (txType: TxType): string => {
    return transactionTypeMap.get(txType) ?? txType;
  };
};
