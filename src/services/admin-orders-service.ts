import {
  addDoc,
  collection,
  getDocs,
  query,
  serverTimestamp,
  where,
} from 'firebase/firestore';

import { getAppConfig } from '@/api/app-config-api';
import type {
  CollectionEntity,
  OrderFees,
  UserEntity,
} from '@/marketplace-shared';
import {
  APP_USERS_COLLECTION,
  COLLECTION_NAME,
  firebaseTimestampToDate,
  formatDateToFirebaseTimestamp,
  ORDERS_COLLECTION_NAME,
  OrderStatus,
} from '@/marketplace-shared';
import { firestore } from '@/root-context';

import { getNextCounterValue } from '../api/counter-api';

export interface BatchCreateOrdersRequest {
  totalOrders: number;
  sellOrdersPercentage: number;
  buyOrdersPercentage: number;
  minPrice: number;
  maxPrice: number;
  priceStep: number;
  useRandomCollection: boolean;
  specificCollectionId?: string;
}

export interface BatchCreateOrdersResponse {
  success: boolean;
  message: string;
  createdOrders: number;
  buyOrders: number;
  sellOrders: number;
}

function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomPrice(
  minPrice: number,
  maxPrice: number,
  step: number = 1,
): number {
  // If step is 0 or minPrice equals maxPrice, return minPrice (single price)
  if (step === 0 || minPrice === maxPrice) {
    return minPrice;
  }

  const steps = Math.floor((maxPrice - minPrice) / step);
  const randomStep = Math.floor(Math.random() * (steps + 1));
  return minPrice + randomStep * step;
}

function calculateOrderDeadline(collection: CollectionEntity): Date | null {
  if (!collection.launchedAt) {
    return null;
  }

  const launchedAt =
    collection.launchedAt instanceof Date
      ? collection.launchedAt
      : firebaseTimestampToDate(collection.launchedAt);

  const currentDate = new Date();
  const launchPlus28Days = new Date(
    launchedAt.getTime() + 28 * 24 * 60 * 60 * 1000,
  );
  const currentPlus7Days = new Date(
    currentDate.getTime() + 7 * 24 * 60 * 60 * 1000,
  );

  return new Date(
    Math.min(launchPlus28Days.getTime(), currentPlus7Days.getTime()),
  );
}

async function getNextOrderNumber(isAdminUser = true): Promise<number> {
  return await getNextCounterValue('order_number', isAdminUser);
}

async function createFeesSnapshot(): Promise<OrderFees> {
  const appConfig = await getAppConfig();
  if (!appConfig) {
    return {
      buyer_locked_percentage: 0,
      seller_locked_percentage: 0,
      purchase_fee: 0,
      referrer_fee: 0,
      order_cancellation_fee: 0,
      resell_purchase_fee: 0,
      resell_purchase_fee_for_seller: 0,
    };
  }

  return {
    buyer_locked_percentage: appConfig.buyer_lock_percentage,
    seller_locked_percentage: appConfig.seller_lock_percentage,
    purchase_fee: appConfig.purchase_fee,
    referrer_fee: appConfig.referrer_fee,
    order_cancellation_fee: appConfig.cancel_order_fee,
    resell_purchase_fee: appConfig.resell_purchase_fee,
    resell_purchase_fee_for_seller: appConfig.resell_purchase_fee_for_seller,
  };
}

export async function createBatchOrdersDirectly(
  request: BatchCreateOrdersRequest,
): Promise<BatchCreateOrdersResponse> {
  try {
    const {
      totalOrders,
      buyOrdersPercentage,
      sellOrdersPercentage,
      minPrice,
      maxPrice,
    } = request;

    if (buyOrdersPercentage + sellOrdersPercentage !== 100) {
      throw new Error('Buy and sell percentages must sum to 100%');
    }

    const adminQuery = query(
      collection(firestore, APP_USERS_COLLECTION),
      where('role', '==', 'admin'),
    );
    const adminSnapshot = await getDocs(adminQuery);

    if (adminSnapshot.empty) {
      throw new Error('No admin users found');
    }

    const adminUsers: UserEntity[] = [];
    adminSnapshot.forEach((doc) => {
      adminUsers.push({ id: doc.id, ...doc.data() } as UserEntity);
    });

    const collectionsSnapshot = await getDocs(
      collection(firestore, COLLECTION_NAME),
    );
    const unlaunchedCollections: CollectionEntity[] = [];

    collectionsSnapshot.forEach((doc) => {
      const collectionData = { id: doc.id, ...doc.data() } as CollectionEntity;
      if (!collectionData.launchedAt) {
        unlaunchedCollections.push(collectionData);
      }
    });

    if (unlaunchedCollections.length === 0) {
      throw new Error('No unlaunched collections found');
    }

    const buyOrdersCount = Math.round(
      (totalOrders * buyOrdersPercentage) / 100,
    );
    const sellOrdersCount = totalOrders - buyOrdersCount;

    const createdOrders: string[] = [];

    const fees = await createFeesSnapshot();

    for (let i = 0; i < buyOrdersCount; i++) {
      const randomAdmin = getRandomElement(adminUsers);
      const randomCollection = getRandomElement(unlaunchedCollections);
      const randomPrice = getRandomPrice(minPrice, maxPrice);
      const orderNumber = await getNextOrderNumber();
      const deadline = calculateOrderDeadline(randomCollection);

      // @ts-expect-error note
      const orderData: Omit<OrderEntity, 'id'> = {
        number: orderNumber,
        buyerId: randomAdmin.id,
        collectionId: randomCollection.id,
        price: randomPrice,
        status: OrderStatus.ACTIVE,
        ...(deadline && { deadline: formatDateToFirebaseTimestamp(deadline) }),
        secondaryMarketPrice: null,
        reseller_earnings_for_seller: 0,
        fees,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const orderRef = await addDoc(
        // TODO fix that bithc
        collection(firestore, ORDERS_COLLECTION_NAME),
        {
          ...orderData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        },
      );
      createdOrders.push(orderRef.id);
    }

    for (let i = 0; i < sellOrdersCount; i++) {
      const randomAdmin = getRandomElement(adminUsers);
      const randomCollection = getRandomElement(unlaunchedCollections);
      const randomPrice = getRandomPrice(minPrice, maxPrice);
      const orderNumber = await getNextOrderNumber();
      const deadline = calculateOrderDeadline(randomCollection);
      // @ts-expect-error note
      const orderData: Omit<OrderEntity, 'id'> = {
        number: orderNumber,
        sellerId: randomAdmin.id,
        collectionId: randomCollection.id,
        price: randomPrice,
        status: OrderStatus.ACTIVE,
        ...(deadline && { deadline: formatDateToFirebaseTimestamp(deadline) }),
        secondaryMarketPrice: null,
        reseller_earnings_for_seller: 0,
        fees,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const orderRef = await addDoc(
        collection(firestore, ORDERS_COLLECTION_NAME),
        {
          ...orderData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        },
      );
      createdOrders.push(orderRef.id);
    }

    return {
      success: true,
      message: `Successfully created ${totalOrders} orders (${buyOrdersCount} buy orders, ${sellOrdersCount} sell orders)`,
      createdOrders: totalOrders,
      buyOrders: buyOrdersCount,
      sellOrders: sellOrdersCount,
    };
  } catch (error) {
    console.error('Error creating batch orders:', error);
    throw error;
  }
}

export async function createBatchOrdersWithProgress(
  request: BatchCreateOrdersRequest,
  onProgress?: (created: number, total: number) => void,
): Promise<BatchCreateOrdersResponse> {
  try {
    const {
      totalOrders,
      buyOrdersPercentage,
      sellOrdersPercentage,
      minPrice,
      maxPrice,
      priceStep,
      useRandomCollection,
      specificCollectionId,
    } = request;

    if (buyOrdersPercentage + sellOrdersPercentage !== 100) {
      throw new Error('Buy and sell percentages must sum to 100%');
    }

    const adminQuery = query(
      collection(firestore, APP_USERS_COLLECTION),
      where('role', '==', 'admin'),
    );
    const adminSnapshot = await getDocs(adminQuery);

    if (adminSnapshot.empty) {
      throw new Error('No admin users found');
    }

    const adminUsers: UserEntity[] = [];
    adminSnapshot.forEach((doc) => {
      adminUsers.push({ id: doc.id, ...doc.data() } as UserEntity);
    });

    let collectionsToUse: CollectionEntity[] = [];

    if (useRandomCollection) {
      const collectionsSnapshot = await getDocs(
        collection(firestore, COLLECTION_NAME),
      );
      const unlaunchedCollections: CollectionEntity[] = [];

      collectionsSnapshot.forEach((doc) => {
        const collectionData = {
          id: doc.id,
          ...doc.data(),
        } as CollectionEntity;
        if (!collectionData.launchedAt) {
          unlaunchedCollections.push(collectionData);
        }
      });

      if (unlaunchedCollections.length === 0) {
        throw new Error('No unlaunched collections found');
      }
      collectionsToUse = unlaunchedCollections;
    } else {
      if (!specificCollectionId) {
        throw new Error(
          'Specific collection ID is required when not using random collection',
        );
      }

      const specificCollectionSnapshot = await getDocs(
        query(
          collection(firestore, COLLECTION_NAME),
          where('__name__', '==', specificCollectionId),
        ),
      );

      if (specificCollectionSnapshot.empty) {
        throw new Error('Specified collection not found');
      }

      const collectionData = {
        id: specificCollectionSnapshot.docs[0].id,
        ...specificCollectionSnapshot.docs[0].data(),
      } as CollectionEntity;

      collectionsToUse = [collectionData];
    }

    const buyOrdersCount = Math.round(
      (totalOrders * buyOrdersPercentage) / 100,
    );
    const sellOrdersCount = totalOrders - buyOrdersCount;

    const createdOrders: string[] = [];
    let createdCount = 0;

    const fees = await createFeesSnapshot();

    for (let i = 0; i < buyOrdersCount; i++) {
      const randomAdmin = getRandomElement(adminUsers);
      const selectedCollection = useRandomCollection
        ? getRandomElement(collectionsToUse)
        : collectionsToUse[0];
      const randomPrice = getRandomPrice(minPrice, maxPrice, priceStep);
      const orderNumber = await getNextOrderNumber();
      const deadline = calculateOrderDeadline(selectedCollection);

      // @ts-expect-error note
      const orderData: Omit<OrderEntity, 'id'> = {
        number: orderNumber,
        buyerId: randomAdmin.id,
        collectionId: selectedCollection.id,
        price: randomPrice,
        status: OrderStatus.ACTIVE,
        ...(deadline && { deadline: formatDateToFirebaseTimestamp(deadline) }),
        secondaryMarketPrice: null,
        reseller_earnings_for_seller: 0,
        fees,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const orderRef = await addDoc(
        collection(firestore, ORDERS_COLLECTION_NAME),
        {
          ...orderData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        },
      );
      createdOrders.push(orderRef.id);
      createdCount++;

      if (onProgress) {
        onProgress(createdCount, totalOrders);
      }
    }

    for (let i = 0; i < sellOrdersCount; i++) {
      const randomAdmin = getRandomElement(adminUsers);
      const selectedCollection = useRandomCollection
        ? getRandomElement(collectionsToUse)
        : collectionsToUse[0];
      const randomPrice = getRandomPrice(minPrice, maxPrice, priceStep);
      const orderNumber = await getNextOrderNumber();
      const deadline = calculateOrderDeadline(selectedCollection);

      // @ts-expect-error note
      const orderData: Omit<OrderEntity, 'id'> = {
        number: orderNumber,
        sellerId: randomAdmin.id,
        collectionId: selectedCollection.id,
        price: randomPrice,
        status: OrderStatus.ACTIVE,
        ...(deadline && { deadline: formatDateToFirebaseTimestamp(deadline) }),
        secondaryMarketPrice: null,
        reseller_earnings_for_seller: 0,
        fees,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const orderRef = await addDoc(
        collection(firestore, ORDERS_COLLECTION_NAME),
        {
          ...orderData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        },
      );
      createdOrders.push(orderRef.id);
      createdCount++;

      if (onProgress) {
        onProgress(createdCount, totalOrders);
      }
    }

    return {
      success: true,
      message: `Successfully created ${totalOrders} orders (${buyOrdersCount} buy orders, ${sellOrdersCount} sell orders)`,
      createdOrders: totalOrders,
      buyOrders: buyOrdersCount,
      sellOrders: sellOrdersCount,
    };
  } catch (error) {
    console.error('Error creating batch orders with progress:', error);
    throw error;
  }
}
