'use client';

import { httpsCallable } from 'firebase/functions';
import { useState } from 'react';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';

import { formatServerError } from '@/api/server-error-handler';
import { Button } from '@/components/ui/button';
import { BaseDrawer } from '@/components/ui/drawer/base-drawer';
import { DrawerHeader } from '@/components/ui/drawer/drawer-header';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import type { GiftEntity } from '@/marketplace-shared';
import { firebaseFunctions, useRootContext } from '@/root-context';

import { createSellOrderDrawerMessages } from './intl/create-sell-order-drawer.messages';

interface CreateSellOrderFromGiftDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  gift: GiftEntity | null;
  onOrderCreated?: () => void;
}

export function CreateSellOrderFromGiftDrawer({
  open,
  onOpenChange,
  gift,
  onOrderCreated,
}: CreateSellOrderFromGiftDrawerProps) {
  const { formatMessage: t } = useIntl();
  const { collections } = useRootContext();
  const [price, setPrice] = useState('');
  const [creating, setCreating] = useState(false);

  const collection = gift
    ? collections.find((c) => c.id === gift.collectionId)
    : null;

  const handleCreateOrder = async () => {
    if (!gift || !price || Number(price) <= 0) {
      toast.error('Please enter a valid price');
      return;
    }

    try {
      setCreating(true);

      const createSellOrderFromGift = httpsCallable(
        firebaseFunctions,
        'createSellOrderFromGift',
      );

      const result = await createSellOrderFromGift({
        giftId: gift.id,
        price: Number(price),
      });

      const data = result.data as { success: boolean; message: string };

      if (data.success) {
        toast.success(t(createSellOrderDrawerMessages.successMessage));
        onOrderCreated?.();
        onOpenChange(false);
        setPrice('');
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      console.error('Error creating sell order:', error);
      toast.error(formatServerError(error, t));
    } finally {
      setCreating(false);
    }
  };

  const handleClose = () => {
    onOpenChange(false);
    setPrice('');
  };

  return (
    <BaseDrawer open={open} onOpenChange={handleClose}>
      <DrawerHeader title={t(createSellOrderDrawerMessages.createSellOrder)} />

      <div className="p-4 space-y-6">
        {gift && collection && (
          <>
            <div className="flex items-center space-x-4">
              <div>
                <p className="text-sm text-muted-foreground">
                  {t(createSellOrderDrawerMessages.setPrice, {
                    giftName: collection.name,
                  })}
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="price">
                {t(createSellOrderDrawerMessages.priceLabel)}
              </Label>
              <Input
                id="price"
                type="number"
                value={price}
                onChange={(e) => setPrice(e.target.value)}
                placeholder="0.00"
                min="0"
                step="0.01"
                disabled={creating}
              />
            </div>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={handleClose}
                disabled={creating}
                className="flex-1"
              >
                {t(createSellOrderDrawerMessages.cancel)}
              </Button>
              <Button
                onClick={handleCreateOrder}
                disabled={creating || !price || Number(price) <= 0}
                className="flex-1"
              >
                {creating
                  ? t(createSellOrderDrawerMessages.creating)
                  : t(createSellOrderDrawerMessages.createOrder)}
              </Button>
            </div>
          </>
        )}
      </div>
    </BaseDrawer>
  );
}
