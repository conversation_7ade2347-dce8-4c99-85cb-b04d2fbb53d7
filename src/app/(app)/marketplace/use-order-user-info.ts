import { useCallback, useEffect, useState } from 'react';

import { getUserById } from '@/api/auth-api';
import type { UserEntity } from '@/marketplace-shared';
import { UserType } from '@/marketplace-shared';

interface UseOrderUserInfoProps {
  userId: string | undefined;
  isOpen: boolean;
}

export function useOrderUserInfo({ userId, isOpen }: UseOrderUserInfoProps) {
  const [userInfo, setUserInfo] = useState<UserEntity | null>(null);
  const [loading, setLoading] = useState(false);

  const loadUserInfo = useCallback(async () => {
    if (!userId) return;

    setLoading(true);
    try {
      const user = await getUserById(userId);
      setUserInfo(user);
    } catch (error) {
      console.error('Error loading user info:', error);
      setUserInfo(null);
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    if (isOpen && userId) {
      loadUserInfo();
    } else {
      setUserInfo(null);
      setLoading(false);
    }
  }, [isOpen, userId, loadUserInfo]);

  const handleClose = useCallback(() => {
    setUserInfo(null);
  }, []);

  return { userInfo, loading, handleClose };
}

export function getUserIdToFetch(
  buyerId: string | undefined,
  sellerId: string | undefined,
  userType: UserType,
): string | undefined {
  return userType === UserType.SELLER ? buyerId : sellerId;
}
