'use client';

import { useIntl } from 'react-intl';

import { ConfirmWrapper } from '@/components/shared/confirm-wrapper';
import { TonLogo } from '@/components/TonLogo';
import { Button } from '@/components/ui/button';

import { createOrderDrawerMessages } from './intl/create-order-drawer.messages';

interface CreateOrderActionsProps {
  onCreateOrder: () => void;
  onCancel: () => void;
  isValidPrice: boolean;
  hasSufficientBalance: boolean;
  loading: boolean;
  price: number;
}

export function CreateOrderActions({
  onCreateOrder,
  onCancel,
  isValidPrice,
  hasSufficientBalance,
  loading,
  price,
}: CreateOrderActionsProps) {
  const { formatMessage: t } = useIntl();

  const isDisabled = !isValidPrice || !hasSufficientBalance || loading;

  return (
    <div className="space-y-3 pt-4">
      <ConfirmWrapper>
        <Button
          onClick={onCreateOrder}
          disabled={isDisabled}
          className="w-full h-12 bg-primary hover:bg-primary/90 text-primary-foreground border-0 rounded-2xl text-base"
        >
          {loading ? (
            t(createOrderDrawerMessages.creating)
          ) : (
            <>
              {t(createOrderDrawerMessages.create)}
              {isValidPrice && (
                <>
                  {' '}
                  &#40;{price.toFixed(2)} <TonLogo className="-m-2" size={24} />
                  <span className="-ml-1">&#41;</span>
                </>
              )}
            </>
          )}
        </Button>
      </ConfirmWrapper>

      <Button
        variant="destructive"
        onClick={onCancel}
        className="w-full h-12 bg-[var(--color-status-red-bg)] border-[var(--color-status-red-border)] text-white hover:bg-[var(--color-status-red)]/30 hover:text-[var(--color-status-red)] rounded-2xl text-base font-medium flex items-center"
        disabled={loading}
      >
        {t(createOrderDrawerMessages.cancel)}
      </Button>
    </div>
  );
}
