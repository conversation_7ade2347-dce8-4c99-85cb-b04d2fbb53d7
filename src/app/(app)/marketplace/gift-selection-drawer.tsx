'use client';

import { Gift } from 'lucide-react';
import { useIntl } from 'react-intl';

import { TgsOrImageGift } from '@/components/tgs/tgs-or-image-gift';
import { BaseDrawer } from '@/components/ui/drawer/base-drawer';
import { DrawerHeader } from '@/components/ui/drawer/drawer-header';
import type { GiftEntity } from '@/marketplace-shared';

import { createOrderDrawerMessages } from './create-order-drawer/intl/create-order-drawer.messages';

interface GiftSelectionDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  gifts: GiftEntity[];
  onGiftSelect: (gift: GiftEntity) => void;
}

export function GiftSelectionDrawer({
  open,
  onOpenChange,
  gifts,
  onGiftSelect,
}: GiftSelectionDrawerProps) {
  const { formatMessage: t } = useIntl();

  return (
    <BaseDrawer open={open} onOpenChange={onOpenChange} zIndex={110}>
      <DrawerHeader
        icon={Gift}
        title={t(createOrderDrawerMessages.selectGift)}
        subtitle={t(createOrderDrawerMessages.selectGiftSubtitle)}
      />

      <div className="space-y-3">
        {gifts.map((gift) => (
          <div
            key={gift.id}
            onClick={() => onGiftSelect(gift)}
            className="p-3 bg-card/50 border border-border/50 rounded-lg hover:bg-card hover:border-primary cursor-pointer transition-colors"
          >
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-lg overflow-hidden bg-background flex items-center justify-center">
                <TgsOrImageGift
                  isImage={true}
                  gift={gift}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="text-foreground font-medium">
                    {gift.model?.name || 'Gift'}
                  </span>
                  <span className="text-muted-foreground text-sm">
                    #{gift.symbol?.name || gift.id}
                  </span>
                </div>
                {gift.backdrop?.name && (
                  <div className="text-muted-foreground text-sm">
                    {t(createOrderDrawerMessages.backdrop)} {gift.backdrop.name}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </BaseDrawer>
  );
}
