'use client';

import { Gift, ShoppingCart, Store, User } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useIntl } from 'react-intl';

import { GlassWrapper } from '@/components/shared/glass-wrapper';
import { AppRoutes } from '@/core.constants';
import { useThemeSwitcher } from '@/hooks/use-theme-switcher';
import { cn } from '@/lib/utils';

import { rootLayoutFooterMessages } from './intl/root-layout-footer.messages';

export default function RootLayoutFooter() {
  const { formatMessage: t } = useIntl();
  const pathname = usePathname();
  const { theme } = useThemeSwitcher();

  const allNavItems = [
    {
      icon: Store,
      label: t(rootLayoutFooterMessages.marketplace),
      route: AppRoutes.MARKETPLACE,
      active: pathname === AppRoutes.MARKETPLACE,
    },
    {
      icon: ShoppingCart,
      label: t(rootLayoutFooterMessages.myOrders),
      route: AppRoutes.ORDERS,
      active: pathname === AppRoutes.ORDERS,
    },
    {
      icon: Gift,
      label: t(rootLayoutFooterMessages.myGifts),
      route: AppRoutes.GIFTS,
      active: pathname === AppRoutes.GIFTS,
    },
    {
      icon: User,
      label: t(rootLayoutFooterMessages.myProfile),
      route: AppRoutes.PROFILE,
      active: pathname === AppRoutes.PROFILE,
    },
  ];

  const navItems = allNavItems;

  // Get theme-aware colors for navigation items
  const getNavItemColors = (isActive: boolean) => {
    if (isActive) {
      return {
        base: 'text-foreground',
        border:
          theme === 'black'
            ? 'border-[var(--color-tg-button)] shadow-[0_0_8px_rgba(218,165,32,0.3)]'
            : theme === 'dark'
              ? 'border-primary shadow-[0_0_8px_rgba(106,178,242,0.2)]'
              : 'border-primary shadow-[0_0_4px_rgba(0,122,255,0.2)]',
        background:
          theme === 'black'
            ? 'bg-[var(--color-tg-button)]/10'
            : 'bg-primary/10',
      };
    }

    return {
      base:
        theme === 'black'
          ? 'text-foreground/80' // More visible white in black theme
          : theme === 'dark'
            ? 'text-foreground/70' // More visible light text in dark theme
            : 'text-foreground/60', // More visible dark text in light theme
      hover:
        theme === 'black'
          ? 'hover:text-[var(--color-tg-button)] hover:[&>div]:scale-110'
          : theme === 'dark'
            ? 'hover:text-primary hover:[&>div]:scale-110'
            : 'hover:text-primary hover:[&>div]:scale-110',
      background: 'hover:bg-accent/10',
    };
  };

  return (
    <footer className="fixed bottom-0 left-0 right-0 z-50">
      <GlassWrapper
        variant="footer"
        className={cn('min-h-[50px] rounded-none hover:rounded-none')}
      >
        <div className="flex items-center justify-between h-full w-full px-2 pb-4">
          {navItems.map((item) => {
            const IconComponent = item.icon;
            const colors = getNavItemColors(item.active);

            return (
              <Link
                key={item.route}
                href={item.route}
                className={cn(
                  'flex flex-col flex-1 items-center gap-1 p-3 h-auto transition-all duration-300 ease-out transform rounded-xl',
                  colors.base,
                  item.active
                    ? ['border-2 scale-105', colors.border, colors.background]
                    : [
                        'border-2 border-transparent',
                        colors.hover,
                        colors.background,
                      ],
                )}
              >
                <div className="transition-all duration-300">
                  <IconComponent
                    className={cn(
                      'transition-all duration-300',
                      item.active ? 'w-6 h-6' : 'w-5 h-5',
                    )}
                  />
                </div>
                {/* <span
                  className={cn(
                    'text-xs font-medium transition-all duration-300 text-center leading-tight',
                    item.active ? 'opacity-100' : 'opacity-70',
                  )}
                >
                  {item.label}
                </span> */}
              </Link>
            );
          })}
        </div>
      </GlassWrapper>
    </footer>
  );
}
