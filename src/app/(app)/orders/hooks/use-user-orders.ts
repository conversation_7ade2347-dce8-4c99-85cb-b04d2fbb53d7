import type { DocumentSnapshot } from 'firebase/firestore';
import { useCallback, useRef, useState } from 'react';

import { getUserOrdersPaginated } from '@/api/order-api';
import type { OrderEntity } from '@/marketplace-shared';
import { UserType } from '@/marketplace-shared';

interface UserOrdersState {
  orders: OrderEntity[];
  loading: boolean;
  loadingMore: boolean;
  hasMore: boolean;
}

const createInitialState = (): UserOrdersState => ({
  orders: [],
  loading: false,
  loadingMore: false,
  hasMore: true,
});

export const useUserOrders = (userId: string | undefined) => {
  const [buyOrdersState, setBuyOrdersState] =
    useState<UserOrdersState>(createInitialState());
  const [sellOrdersState, setSellOrdersState] =
    useState<UserOrdersState>(createInitialState());

  const buyOrdersLastDocRef = useRef<DocumentSnapshot | null>(null);
  const sellOrdersLastDocRef = useRef<DocumentSnapshot | null>(null);

  const getUserRole = useCallback(
    (order: OrderEntity) => {
      return order.sellerId === userId ? UserType.SELLER : UserType.BUYER;
    },
    [userId],
  );

  const loadOrders = useCallback(
    async (orderType: 'buy' | 'sell', reset = true) => {
      if (!userId) return;

      const isBuyOrders = orderType === 'buy';
      const setState = isBuyOrders ? setBuyOrdersState : setSellOrdersState;
      const lastDocRef = isBuyOrders
        ? buyOrdersLastDocRef
        : sellOrdersLastDocRef;

      if (reset) {
        setState((prev) => ({
          ...prev,
          loading: true,
          orders: [],
          hasMore: true,
        }));
        lastDocRef.current = null;
      } else {
        setState((prev) => ({ ...prev, loadingMore: true }));
      }

      try {
        const filters = {
          limit: 40, // Fetch more to account for client-side filtering
          lastDoc: reset ? null : lastDocRef.current,
        };

        const result = await getUserOrdersPaginated(userId, filters);

        // Filter orders based on user role
        const filteredOrders = result.orders.filter((order) => {
          const role = getUserRole(order);
          return isBuyOrders
            ? role === UserType.BUYER
            : role === UserType.SELLER;
        });

        // Take only the first 20 after filtering
        const ordersToShow = filteredOrders.slice(0, 20);
        // Only has more if we have exactly 20 orders AND there are more from the API
        const hasMoreFiltered = filteredOrders.length === 20 && result.hasMore;

        setState((prev) => {
          if (reset) {
            return {
              ...prev,
              orders: ordersToShow,
              hasMore: hasMoreFiltered,
            };
          } else {
            // Deduplicate orders by ID
            const existingIds = new Set(prev.orders.map((order) => order.id));
            const newOrders = ordersToShow.filter(
              (order) => !existingIds.has(order.id),
            );
            return {
              ...prev,
              orders: [...prev.orders, ...newOrders],
              hasMore: hasMoreFiltered,
            };
          }
        });

        lastDocRef.current = result.lastDoc;
      } catch (error) {
        console.error(`Error loading ${orderType} orders:`, error);
      } finally {
        setState((prev) => ({
          ...prev,
          loading: false,
          loadingMore: false,
        }));
      }
    },
    [userId, getUserRole],
  );

  const loadMoreOrders = useCallback(
    (orderType: 'buy' | 'sell') => {
      const currentState =
        orderType === 'buy' ? buyOrdersState : sellOrdersState;
      const isLoading = currentState.loading || currentState.loadingMore;

      if (currentState.hasMore && !isLoading) {
        loadOrders(orderType, false);
      }
    },
    [buyOrdersState, sellOrdersState, loadOrders],
  );

  const resetOrders = useCallback(() => {
    setBuyOrdersState(createInitialState());
    setSellOrdersState(createInitialState());
    buyOrdersLastDocRef.current = null;
    sellOrdersLastDocRef.current = null;
  }, []);

  return {
    buyOrdersState,
    sellOrdersState,
    loadOrders,
    loadMoreOrders,
    resetOrders,
    getUserRole,
  };
};
