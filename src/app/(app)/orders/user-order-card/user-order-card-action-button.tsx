import { DollarSign, Gift, Link, Package, Trash2 } from 'lucide-react';
import { useIntl } from 'react-intl';

import { Button } from '@/components/ui/button';

import type { ButtonConfig } from './user-order-card-button-config';

interface ActionButtonProps {
  config: ButtonConfig;
}

export function ActionButton({ config }: ActionButtonProps) {
  const { formatMessage: t } = useIntl();

  if (!config.show) return null;

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    config.handler?.();
  };

  // Determine icon based on data attribute
  const getIcon = () => {
    switch (config.dataAttr) {
      case 'data-attach-gift-button':
        return <Link className="w-4 h-4" />;
      case 'data-resell-button':
        return <DollarSign className="w-4 h-4" />;
      case 'data-get-gift-button':
        return <Package className="w-4 h-4" />;
      case 'data-get-cancelled-gift-button':
        return <Trash2 className="w-4 h-4" />;
      case 'data-send-gift-button':
      default:
        return <Gift className="w-4 h-4" />;
    }
  };

  return (
    <Button
      {...{ [config.dataAttr]: true }}
      onClick={handleClick}
      className={`mt-2 w-full ${config.className} text-white text-sm py-2`}
    >
      {getIcon()}
      {t(config.message)}
    </Button>
  );
}
