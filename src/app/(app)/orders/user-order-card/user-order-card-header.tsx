import { StatusBadge } from '@/components/shared/order-status-badge';
import type { OrderEntity } from '@/marketplace-shared';

interface UserOrderCardHeaderProps {
  order: OrderEntity;
}

export function UserOrderCardHeader({ order }: UserOrderCardHeaderProps) {
  return (
    <div className="flex w-full items-center justify-start mb-1 absolute top-0 left-0 px-2 pt-1.5 z-50">
      <StatusBadge status={order.status} variant="pill" />
    </div>
  );
}
