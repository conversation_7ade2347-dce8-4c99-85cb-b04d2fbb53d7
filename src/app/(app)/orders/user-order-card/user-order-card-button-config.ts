import type { OrderEntity } from '@/marketplace-shared';
import { OrderStatus, UserType } from '@/marketplace-shared';

import { userOrderCardMessages } from './intl/user-order-card.messages';

/**
 * User Order Card Button Configuration
 *
 * Theme-aware button styling:
 * - Default/Dark themes: Use semantic colors (purple, green, red, primary)
 * - Black theme:
 *   - Send/Get Gift: Gold color (--color-tg-button)
 *   - Attach Gift: Gold color (--color-tg-button)
 *   - Resell Order: Green color (--color-status-green)
 *   - Get Cancelled: Red color (destructive)
 * - All buttons include theme-specific glow effects
 * - Smooth transitions and hover states for better UX
 * - Icons: Gift, Package, DollarSign, Link, Trash2 for different actions
 */

export interface ButtonConfig {
  show: boolean;
  handler?: () => void;
  className: string;
  message: any;
  dataAttr: string;
}

export interface UserOrderCardButtonProps {
  order: OrderEntity;
  userType: UserType;
  hasGift: boolean;
  onSendAGiftClick?: () => void;
  onGetAGiftClick?: () => void;
  onResellOrder?: () => void;
  onActivateOrder?: () => void;
  onGetCancelledGift?: () => void;
  onAttachGiftClick?: () => void;
}

export function createButtonConfig({
  order,
  userType,
  hasGift,
  onSendAGiftClick,
  onGetAGiftClick,
  onResellOrder,
  onGetCancelledGift,
  onAttachGiftClick,
}: UserOrderCardButtonProps): Record<string, ButtonConfig> {
  return {
    sendGift: {
      show: !!(
        order.deadline &&
        onSendAGiftClick &&
        userType === UserType.SELLER &&
        order.status === OrderStatus.PAID
      ),
      handler: onSendAGiftClick,
      className:
        'bg-[var(--color-status-purple)] hover:bg-[var(--color-status-purple)]/90 text-white shadow-sm hover:shadow-md transition-all duration-200 black:shadow-[0_2px_8px_rgba(168,85,247,0.3)] dark:shadow-[0_2px_8px_rgba(168,85,247,0.2)] black:bg-[var(--color-tg-button)] black:text-[var(--color-tg-button-text)] black:hover:bg-[var(--color-tg-button)]/90',
      message: userOrderCardMessages.sendAGift,
      dataAttr: 'data-send-gift-button',
    },
    getGift: {
      show: !!(
        onGetAGiftClick &&
        userType === UserType.BUYER &&
        order.status === OrderStatus.GIFT_SENT_TO_RELAYER
      ),
      handler: onGetAGiftClick,
      className:
        'bg-[var(--color-status-purple)] hover:bg-[var(--color-status-purple)]/90 text-white shadow-sm hover:shadow-md transition-all duration-200 black:shadow-[0_2px_8px_rgba(168,85,247,0.3)] dark:shadow-[0_2px_8px_rgba(168,85,247,0.2)] black:bg-[var(--color-tg-button)] black:text-[var(--color-tg-button-text)] black:hover:bg-[var(--color-tg-button)]/90',
      message: userOrderCardMessages.getAGift,
      dataAttr: 'data-get-gift-button',
    },
    resell: {
      show: !!(
        userType === UserType.BUYER &&
        order.status === OrderStatus.GIFT_SENT_TO_RELAYER &&
        onResellOrder
      ),
      handler: onResellOrder,
      className:
        'bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm hover:shadow-md transition-all duration-200 black:shadow-[0_2px_8px_rgba(74,222,128,0.3)] dark:shadow-[0_2px_8px_rgba(106,178,242,0.2)] black:bg-[var(--color-status-green)] black:text-white black:hover:bg-[var(--color-status-green)]/90',
      message: userOrderCardMessages.resellThisOrder,
      dataAttr: 'data-resell-button',
    },

    getCancelled: {
      show: !!(
        userType === UserType.SELLER &&
        order.status === OrderStatus.CANCELLED &&
        hasGift &&
        onGetCancelledGift
      ),
      handler: onGetCancelledGift,
      className:
        'bg-[var(--color-status-red)] hover:bg-[var(--color-status-red)]/90 text-white shadow-sm hover:shadow-md transition-all duration-200 black:shadow-[0_2px_8px_rgba(255,68,68,0.3)] dark:shadow-[0_2px_8px_rgba(236,57,66,0.2)] black:bg-destructive black:text-destructive-foreground black:hover:bg-destructive/90',
      message: userOrderCardMessages.getCancelledGift,
      dataAttr: 'data-get-cancelled-gift-button',
    },
    attachGift: {
      show: !!(
        userType === UserType.SELLER &&
        ((order.status === OrderStatus.PAID && order.deadline) ||
          order.status === OrderStatus.CREATED) &&
        !hasGift &&
        onAttachGiftClick
      ),
      handler: onAttachGiftClick,
      className:
        'bg-[var(--color-status-green)] hover:bg-[var(--color-status-green)]/90 text-white shadow-sm hover:shadow-md transition-all duration-200 black:shadow-[0_2px_8px_rgba(74,222,128,0.3)] dark:shadow-[0_2px_8px_rgba(74,222,128,0.2)] black:bg-[var(--color-tg-button)] black:text-[var(--color-tg-button-text)] black:hover:bg-[var(--color-tg-button)]/90',
      message: userOrderCardMessages.attachGift,
      dataAttr: 'data-attach-gift-button',
    },
  };
}
