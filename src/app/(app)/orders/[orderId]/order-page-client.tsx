'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';

import { getOrderById } from '@/api/order-api';
import { OrderDetailsContent } from '@/components/order-details/order-details-content';
import type { CollectionEntity, OrderEntity } from '@/marketplace-shared';
import { UserType } from '@/marketplace-shared';
import { useRootContext } from '@/root-context';
import { isSecondaryMarketOrder } from '@/services/order-service';

import { orderPageClientMessages } from './intl/order-page-client.messages';

interface OrderPageClientProps {
  orderId: string;
}

export function OrderPageClient({ orderId }: OrderPageClientProps) {
  const { formatMessage: t } = useIntl();
  const router = useRouter();
  const { collections, currentUser } = useRootContext();
  const [order, setOrder] = useState<OrderEntity | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrder = async () => {
      try {
        setLoading(true);
        setError(null);

        const fetchedOrder = await getOrderById(orderId);

        if (!fetchedOrder) {
          // Order not found, redirect to root
          router.replace('/');
          return;
        }

        setOrder(fetchedOrder);
      } catch (err) {
        console.error('Error fetching order:', err);
        setError(t(orderPageClientMessages.failedToLoadOrder));
        // On error, redirect to root after a short delay
        setTimeout(() => {
          router.replace('/');
        }, 2000);
      } finally {
        setLoading(false);
      }
    };

    if (orderId) {
      fetchOrder();
    }
  }, [orderId, router]);

  const collection: CollectionEntity | null = order
    ? collections.find((c) => c.id === order.collectionId) || null
    : null;

  const handleOrderAction = () => {
    router.push('/');
  };

  const handleCancel = () => {
    router.push('/');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#0098EA] mx-auto mb-4"></div>
          <p className="text-[#708499]">Loading order...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-400 mb-2">{error}</p>
          <p className="text-[#708499] text-sm">Redirecting to home...</p>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-muted-foreground">
            {t(orderPageClientMessages.orderNotFound)}
          </p>
          <p className="text-muted-foreground text-sm">
            {t(orderPageClientMessages.redirectingToHome)}
          </p>
        </div>
      </div>
    );
  }

  const isSecondary = isSecondaryMarketOrder(order, currentUser);

  const userType = isSecondary
    ? UserType.BUYER
    : order?.buyerId
      ? UserType.BUYER
      : UserType.SELLER;

  return (
    <div className="max-w-2xl mx-auto p-4">
      <OrderDetailsContent
        userType={userType}
        order={order}
        collection={collection}
        onOrderAction={handleOrderAction}
        onClose={handleCancel}
        showCloseButton={true}
      />
    </div>
  );
}
