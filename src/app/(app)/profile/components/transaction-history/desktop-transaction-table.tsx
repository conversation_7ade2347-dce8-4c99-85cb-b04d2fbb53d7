import { useIntl } from 'react-intl';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  firebaseTimestampToDate,
  type UserTxEntity,
} from '@/marketplace-shared';
import { getTransactionDescription } from '@/services/transaction-description-service';
import { useTransactionTypeText } from '@/services/transaction-type-service';
import {
  formatAmount,
  formatTransactionDate,
  getAmountColor,
} from '@/utils/transaction-utils';

import { desktopTransactionTableMessages } from './intl/desktop-transaction-table.messages';

interface DesktopTransactionTableProps {
  transactions: UserTxEntity[];
}

export function DesktopTransactionTable({
  transactions,
}: DesktopTransactionTableProps) {
  const intl = useIntl();
  const { formatMessage: t } = intl;
  const getTransactionTypeText = useTransactionTypeText();

  return (
    <div className="hidden md:block bg-card border border-border rounded-lg overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow className="border-border">
            <TableHead className="text-foreground">
              {t(desktopTransactionTableMessages.type)}
            </TableHead>
            <TableHead className="text-foreground">
              {t(desktopTransactionTableMessages.amount)}
            </TableHead>
            <TableHead className="text-foreground">
              {t(desktopTransactionTableMessages.date)}
            </TableHead>
            <TableHead className="text-foreground">
              {t(desktopTransactionTableMessages.description)}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.map((transaction) => (
            <TableRow
              key={transaction.id}
              className="border-border hover:bg-input"
            >
              <TableCell className="text-foreground">
                {getTransactionTypeText(transaction.tx_type)}
              </TableCell>
              <TableCell className={getAmountColor(transaction.amount)}>
                {formatAmount(transaction.amount)}
              </TableCell>
              <TableCell className="text-muted-foreground">
                {formatTransactionDate(
                  firebaseTimestampToDate(transaction.createdAt),
                )}
              </TableCell>
              <TableCell className="text-muted-foreground max-w-xs truncate">
                {getTransactionDescription(transaction, intl)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
