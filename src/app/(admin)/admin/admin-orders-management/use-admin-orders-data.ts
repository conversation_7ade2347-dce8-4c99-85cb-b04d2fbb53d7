'use client';

import { useEffect, useState } from 'react';

import type {
  AdminOrderStats,
  GetAdminOrdersPaginatedParams,
} from '@/api/admin-api';
import { getAdminOrdersPaginated, getAdminOrderStats } from '@/api/admin-api';
import { getUserById } from '@/api/user-api';
import { useToast } from '@/hooks/use-toast';
import type { OrderEntity, UserEntity } from '@/marketplace-shared';

interface UseAdminOrdersDataProps {
  pageSize: number;
  sortKey?: string | null;
  sortDirection?: string | null;
}

interface UseAdminOrdersDataReturn {
  // Stats
  stats: AdminOrderStats | null;
  loadingStats: boolean;
  loadStats: () => Promise<void>;

  // Orders
  orders: OrderEntity[];
  loadingOrders: boolean;
  currentPage: number;
  totalPages: number;
  userCache: Record<string, UserEntity>;

  // Actions
  loadOrders: (page?: number) => Promise<void>;
  setCurrentPage: (page: number) => void;
  refreshData: () => Promise<void>;
}

export function useAdminOrdersData({
  pageSize,
  sortKey,
  sortDirection,
}: UseAdminOrdersDataProps): UseAdminOrdersDataReturn {
  const { toast } = useToast();

  // Stats state
  const [stats, setStats] = useState<AdminOrderStats | null>(null);
  const [loadingStats, setLoadingStats] = useState(true);

  // Orders state
  const [orders, setOrders] = useState<OrderEntity[]>([]);
  const [loadingOrders, setLoadingOrders] = useState(true);
  const [userCache, setUserCache] = useState<Record<string, UserEntity>>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const loadStats = async () => {
    try {
      setLoadingStats(true);
      const result = await getAdminOrderStats();
      setStats(result);
    } catch {
      toast({
        title: 'Error loading stats',
        description: 'Failed to load order statistics',
        variant: 'destructive',
      });
    } finally {
      setLoadingStats(false);
    }
  };

  const loadOrders = async (page: number = currentPage) => {
    try {
      setLoadingOrders(true);
      const params: GetAdminOrdersPaginatedParams = {
        page,
        pageSize,
        sortBy: sortKey || 'createdAt',
        // @ts-expect-error note
        sortDirection: sortDirection || 'desc',
      };
      const result = await getAdminOrdersPaginated(params);
      setOrders(result.orders);
      setTotalPages(Math.ceil(result.totalItems / pageSize));

      // Load user data for orders
      const userIds = new Set<string>();
      result.orders.forEach((order) => {
        if (order.buyerId) userIds.add(order.buyerId);
        if (order.sellerId) userIds.add(order.sellerId);
      });

      const newUserCache = { ...userCache };
      const usersToLoad = Array.from(userIds).filter((id) => !newUserCache[id]);

      if (usersToLoad.length > 0) {
        const userPromises = usersToLoad.map((userId) => getUserById(userId));
        const users = await Promise.all(userPromises);
        users.forEach((user, index) => {
          if (user) {
            newUserCache[usersToLoad[index]] = user;
          }
        });
        setUserCache(newUserCache);
      }
    } catch {
      toast({
        title: 'Error loading orders',
        description: 'Failed to load orders',
        variant: 'destructive',
      });
    } finally {
      setLoadingOrders(false);
    }
  };

  const refreshData = async () => {
    await Promise.all([loadStats(), loadOrders()]);
  };

  // Initial load
  useEffect(() => {
    refreshData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Reload orders when sort changes
  useEffect(() => {
    if (sortKey || sortDirection) {
      loadOrders(1);
      setCurrentPage(1);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sortKey, sortDirection]);

  return {
    // Stats
    stats,
    loadingStats,
    loadStats,

    // Orders
    orders,
    loadingOrders,
    currentPage,
    totalPages,
    userCache,

    // Actions
    loadOrders,
    setCurrentPage,
    refreshData,
  };
}
