'use client';

import { useCallback, useEffect, useState } from 'react';
import { useDebounceValue } from 'usehooks-ts';

import {
  getUsersWithPagination,
  type GetUsersWithPaginationParams,
} from '@/api/user-api';
import { Pagination } from '@/components/ui/pagination';
import { usePagePagination } from '@/hooks/use-page-pagination';
import { useTableSorting } from '@/hooks/use-table-sorting';
import type { UserEntity } from '@/marketplace-shared';

import { UsersHeader } from './users-management/users-header';
import { UsersTable } from './users-management/users-table';

export function UsersManagement() {
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm] = useDebounceValue(searchTerm, 300);

  const { sortKey, sortDirection, handleSort } = useTableSorting({
    defaultSortKey: 'displayName',
    defaultSortDirection: 'asc',
  });

  const fetchUsersPage = useCallback(
    async (page: number, pageSize: number) => {
      const params: GetUsersWithPaginationParams = {
        page,
        pageSize,
        sortBy: sortKey || 'displayName',
        sortDirection: sortDirection || 'asc',
        searchTerm: debouncedSearchTerm || undefined,
      };
      return getUsersWithPagination(params);
    },
    [sortKey, sortDirection, debouncedSearchTerm],
  );

  const {
    items: users,
    loading,
    currentPage,
    totalPages,
    totalItems,
    goToPage,
    loadPage,
    refresh,
  } = usePagePagination<UserEntity>(fetchUsersPage, {
    pageSize: 25,
  });

  useEffect(() => {
    loadPage(1);
  }, [loadPage]);

  // Reload data when sorting changes
  useEffect(() => {
    if (sortKey && sortDirection) {
      refresh();
    }
  }, [sortKey, sortDirection, refresh]);

  // Reload data when debounced search term changes
  useEffect(() => {
    refresh();
  }, [debouncedSearchTerm, refresh]);

  const handlePageChange = (page: number) => {
    goToPage(page);
  };

  const handleSearchChange = useCallback((term: string) => {
    setSearchTerm(term);
  }, []);

  // When searching, we use backend filtering through the API
  const displayUsers = users;

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <UsersHeader onRefresh={refresh} totalUsers={totalItems} />

        <UsersTable
          users={displayUsers}
          loading={loading}
          searchTerm={searchTerm}
          onSearchChange={handleSearchChange}
          sortKey={sortKey}
          sortDirection={sortDirection}
          onSort={handleSort}
        />

        {!debouncedSearchTerm && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            className="mt-4"
          />
        )}
      </div>
    </div>
  );
}
