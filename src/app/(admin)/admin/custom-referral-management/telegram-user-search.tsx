import { useState } from 'react';

import { searchUsersByTelegramHandle } from '@/api/user-api';
import { FormDescription, FormLabel } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import type { UserEntity } from '@/marketplace-shared';

interface TelegramUserSearchProps {
  value: string;
  onUserSelect: (user: UserEntity) => void;
  onSearchChange: (value: string) => void;
}

export const TelegramUserSearch = ({
  value,
  onUserSelect,
  onSearchChange,
}: TelegramUserSearchProps) => {
  const [searchResults, setSearchResults] = useState<UserEntity[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(false);

  const handleSearch = async (searchTerm: string) => {
    onSearchChange(searchTerm);

    if (!searchTerm.trim()) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    try {
      setIsSearching(true);
      const results = await searchUsersByTelegramHandle(searchTerm);
      setSearchResults(results);
      setShowSearchResults(true);
    } catch (error) {
      console.error('Error searching users:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleUserSelect = (user: UserEntity) => {
    onUserSelect(user);
    setShowSearchResults(false);
  };

  return (
    <div className="space-y-2">
      <FormLabel>Search by Telegram Handle</FormLabel>
      <div className="relative">
        <Input
          placeholder="Enter telegram handle (e.g., @username)"
          value={value}
          onChange={(e) => handleSearch(e.target.value)}
        />
        {isSearching && (
          <div className="absolute right-2 top-2">
            <div className="animate-spin h-4 w-4 border-2 border-gray-300 border-t-gray-600 rounded-full"></div>
          </div>
        )}
        {showSearchResults && searchResults.length > 0 && (
          <div className="absolute z-10 w-full mt-1 bg-card border border-border rounded-md shadow-lg max-h-40 overflow-y-auto">
            {searchResults.map((user) => (
              <div
                key={user.id}
                className="px-3 py-2 hover:bg-accent cursor-pointer"
                onClick={() => handleUserSelect(user)}
              >
                <div className="font-medium">@{user.telegram_handle}</div>
                <div className="text-sm text-muted-foreground">
                  {user.displayName} ({user.id.slice(0, 8)}...)
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      <FormDescription>
        Search for a user by their telegram handle to auto-fill the User ID
      </FormDescription>
    </div>
  );
};
