'use client';

import { useState } from 'react';

import { Pagination } from '@/components/ui/pagination';
import { useTableSorting } from '@/hooks/use-table-sorting';
import type { OrderEntity } from '@/marketplace-shared';
import { useRootContext } from '@/root-context';

import { AdminOrdersHeader } from './admin-orders-management/admin-orders-header';
import { AdminOrdersStats } from './admin-orders-management/admin-orders-stats';
import { AdminOrdersStatsSkeleton } from './admin-orders-management/admin-orders-stats-skeleton';
import { AdminOrdersTable } from './admin-orders-management/admin-orders-table';
import { AdminOrdersTableSkeleton } from './admin-orders-management/admin-orders-table-skeleton';
import { EditAdminOrderModal } from './admin-orders-management/edit-admin-order-modal';
import type { StatType } from './admin-orders-management/types';
import { useAdminOrdersActions } from './admin-orders-management/use-admin-orders-actions';
import { useAdminOrdersData } from './admin-orders-management/use-admin-orders-data';
import { useStatsUpdater } from './admin-orders-management/use-stats-updater';

export function AdminOrdersManagement() {
  const { collections } = useRootContext();

  const [editingOrder, setEditingOrder] = useState<OrderEntity | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [updatingStats, setUpdatingStats] = useState<Set<string>>(new Set());
  const [selectedOrderIds, setSelectedOrderIds] = useState<Set<string>>(
    new Set(),
  );

  const {
    sortKey,
    sortDirection,
    handleSort: originalHandleSort,
  } = useTableSorting({
    defaultSortKey: 'createdAt',
    defaultSortDirection: 'desc',
  });

  const pageSize = 25;

  const {
    stats,
    loadingStats,
    orders,
    loadingOrders,
    currentPage,
    totalPages,
    userCache,
    loadOrders,
    setCurrentPage,
    refreshData,
  } = useAdminOrdersData({
    pageSize,
    sortKey,
    sortDirection,
  });

  // Optimized sort handler that only reloads when sort actually changes
  const handleSort = (key: string, direction: 'asc' | 'desc' | null) => {
    if (!direction) return;

    const currentSortKey = sortKey || 'createdAt';
    const currentSortDirection = sortDirection || 'desc';

    // Only reload if sort actually changed
    if (key !== currentSortKey || direction !== currentSortDirection) {
      originalHandleSort(key, direction);
      setSelectedOrderIds(new Set());
    }
  };

  const { handleCancelOrder, handleDeleteOrder } = useAdminOrdersActions();

  const { updateStat } = useStatsUpdater({
    stats,
    setStats: () => {}, // Not needed since we use the hook
    setUpdatingStats,
  });

  const handleUpdateStat = async (statType: string) => {
    await updateStat(statType as StatType);
  };

  const handlePageChange = (page: number) => {
    // Only load if page actually changed
    if (page !== currentPage) {
      setCurrentPage(page);
      setSelectedOrderIds(new Set()); // Clear selection when changing pages
      loadOrders(page);
    }
  };

  const wrappedHandleCancelOrder = async (orderId: string) => {
    try {
      await handleCancelOrder(orderId);
      await refreshData();
    } catch {
      // Error handling is done in the hook
    }
  };

  const wrappedHandleDeleteOrder = async (orderId: string) => {
    try {
      await handleDeleteOrder(orderId);
      await refreshData();
    } catch {
      // Error handling is done in the hook
    }
  };

  const handleEditOrder = (order: OrderEntity) => {
    setEditingOrder(order);
    setIsEditModalOpen(true);
  };

  const handleEditModalClose = () => {
    setIsEditModalOpen(false);
    setEditingOrder(null);
  };

  const handleEditSuccess = async () => {
    await refreshData();
  };

  const handleOrderSelect = (orderId: string, selected: boolean) => {
    setSelectedOrderIds((prev) => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(orderId);
      } else {
        newSet.delete(orderId);
      }
      return newSet;
    });
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedOrderIds(new Set(orders.map((order) => order.id!)));
    } else {
      setSelectedOrderIds(new Set());
    }
  };

  const handleBatchCancel = async (orderIds: string[]) => {
    const promises = orderIds.map((orderId) => handleCancelOrder(orderId));
    await Promise.all(promises);
    setSelectedOrderIds(new Set());
    await refreshData();
  };

  const handleBatchDelete = async (orderIds: string[]) => {
    const promises = orderIds.map((orderId) => handleDeleteOrder(orderId));
    await Promise.all(promises);
    setSelectedOrderIds(new Set());
    await refreshData();
  };

  return (
    <div className="space-y-6">
      {/* Stats Section - Independent Loading */}
      {loadingStats ? (
        <AdminOrdersStatsSkeleton />
      ) : (
        <AdminOrdersStats
          stats={stats}
          onUpdateStat={handleUpdateStat}
          updatingStats={updatingStats}
        />
      )}

      <div className="space-y-4">
        <AdminOrdersHeader
          onRefresh={refreshData}
          selectedOrderIds={selectedOrderIds}
          onBatchCancel={handleBatchCancel}
          onBatchDelete={handleBatchDelete}
        />

        {/* Orders Table Section - Independent Loading */}
        {loadingOrders ? (
          <AdminOrdersTableSkeleton />
        ) : (
          <AdminOrdersTable
            orders={orders}
            userCache={userCache}
            collections={collections}
            onCancelOrder={wrappedHandleCancelOrder}
            onDeleteOrder={wrappedHandleDeleteOrder}
            onEditOrder={handleEditOrder}
            selectedOrderIds={selectedOrderIds}
            onOrderSelect={handleOrderSelect}
            onSelectAll={handleSelectAll}
            sortKey={sortKey}
            sortDirection={sortDirection}
            onSort={handleSort}
          />
        )}

        {/* Pagination - Only show when orders are loaded */}
        {!loadingOrders && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            className="mt-4"
          />
        )}
      </div>

      <EditAdminOrderModal
        isOpen={isEditModalOpen}
        onClose={handleEditModalClose}
        onSuccess={handleEditSuccess}
        order={editingOrder}
        collections={collections}
      />
    </div>
  );
}
