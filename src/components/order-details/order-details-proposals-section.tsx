'use client';

import { useState } from 'react';
import { useIntl } from 'react-intl';

import { CollapsibleSection } from '@/components/shared/collapsible-section';
import type { OrderEntity, UserType } from '@/marketplace-shared';

import { orderDetailsProposalsSectionMessages } from './intl/order-details-proposals-section.messages';
import { ProposalsTable } from './proposals';

interface OrderDetailsProposalsSectionProps {
  order: OrderEntity;
  userType?: UserType;
  onAcceptProposal?: (proposalId: string) => void;
  onCancelProposal?: (proposalId: string) => void;
  acceptingProposalId?: string;
  cancellingProposal?: boolean;
  proposalsRefreshKey?: number;
}

export function OrderDetailsProposalsSection({
  order,
  userType,
  onAcceptProposal,
  onCancelProposal,
  acceptingProposalId,
  cancellingProposal,
  proposalsRefreshKey,
}: OrderDetailsProposalsSectionProps) {
  const { formatMessage: t } = useIntl();
  const [isOpen, setIsOpen] = useState(false);

  return (
    <CollapsibleSection
      title={t(orderDetailsProposalsSectionMessages.priceProposals)}
      defaultOpen={true}
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <ProposalsTable
        key={proposalsRefreshKey}
        order={order}
        userType={userType}
        onAcceptProposal={onAcceptProposal}
        onCancelProposal={onCancelProposal}
        acceptingProposalId={acceptingProposalId}
        cancellingProposal={cancellingProposal}
      />
    </CollapsibleSection>
  );
}
