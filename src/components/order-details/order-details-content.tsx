'use client';

import { Share, TrendingUp, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';

import { makeSecondaryMarketPurchase } from '@/api/orders-api';
import {
  acceptProposal,
  cancelProposal,
  canUserProposeOnOrder,
} from '@/api/proposal-api';
import { formatServerError } from '@/api/server-error-handler';
import { useWalletConnection } from '@/app/(app)/root-layout-header/root-layout-header-hooks';
import { DepositDrawer } from '@/components/deposit-drawer';
import { TonLogo } from '@/components/TonLogo';
import { Button } from '@/components/ui/button';
import { InsufficientBalance } from '@/components/ui/insufficient-balance';
import { ResellTxHistory } from '@/components/ui/order/resell-tx-history';
import { OrderActors } from '@/components/ui/order-actors';
import { preloadSingleGift } from '@/hooks/use-gift-preloader';
import { useOrderGift } from '@/hooks/use-order-gift';
import { useShareLink } from '@/hooks/use-share-link';
import { cn, roundToThreeDecimals } from '@/lib/utils';
import type { CollectionEntity, OrderEntity } from '@/marketplace-shared';
import { OrderStatus, Role, UserType } from '@/marketplace-shared';
import { useRootContext } from '@/root-context';
import {
  calculateBalanceValidation,
  isActiveBuyerOrder,
  isActiveSellerOrder,
  isSecondaryMarketOrder,
  isSecondaryMarketPurchaseEligible,
  shouldShowProposalsSection,
} from '@/services/order-service';
import { executeMarketplaceOrderAction } from '@/utils/order-action-utils';

import { OrderTraitsSection } from '../shared/order-traits-section';
import { orderDetailsContentMessages } from './intl/order-details-content.messages';
import { orderDetailsProposalsSectionMessages } from './intl/order-details-proposals-section.messages';
import {
  OrderDetailsActionButtons,
  OrderDetailsFeesSection,
  OrderDetailsHeaderSection,
  OrderDetailsImageSection,
} from './order-details-drawer/index';
import { orderDetailsActionButtonsMessages } from './order-details-drawer/intl/order-details-action-buttons.messages';
import { OrderDetailsProposalsSection } from './order-details-proposals-section';
import { PriceProposalDrawer } from './proposals';
import { proposalsTableMessages } from './proposals/intl/proposals-table.messages';

interface OrderDetailsContentProps {
  order: OrderEntity;
  collection: CollectionEntity | null;
  userType?: UserType;
  onOrderAction?: () => void;
  hideActionButton?: boolean;
  onClose?: () => void;
  showCloseButton?: boolean;
}

export function OrderDetailsContent({
  order,
  collection,
  userType,
  onOrderAction,
  hideActionButton = false,
  onClose,
  showCloseButton = false,
}: OrderDetailsContentProps) {
  const { formatMessage: t } = useIntl();
  const { currentUser, appConfig, role } = useRootContext();
  const [actionLoading, setActionLoading] = useState(false);
  const [showResellHistory, setShowResellHistory] = useState(false);
  const { handleShare, isSharing } = useShareLink({ orderId: order.id });
  const [showDepositDrawer, setShowDepositDrawer] = useState(false);

  const { tonWalletAddress, handleWalletAction } = useWalletConnection();

  // Proposals state
  const [showPriceProposalDrawer, setShowPriceProposalDrawer] = useState(false);
  const [acceptingProposalId, setAcceptingProposalId] = useState<string | null>(
    null,
  );
  const [cancellingProposal, setCancellingProposal] = useState(false);
  const [proposalsRefreshKey, setProposalsRefreshKey] = useState(0);

  const { hasSufficientBalance } = calculateBalanceValidation({
    order,
    currentUser,
    appConfig,
  });

  const effectiveUserType = userType || UserType.BUYER;

  const handleTopUp = async () => {
    if (!tonWalletAddress) {
      await handleWalletAction();
      return;
    }
    setShowDepositDrawer(true);
  };

  const handleAction = async () => {
    if (!order?.id || !currentUser?.id) return;

    setActionLoading(true);

    let result;

    if (isSecondaryMarketPurchaseEligible(order, currentUser)) {
      try {
        const purchaseResult = await makeSecondaryMarketPurchase(order.id);
        result = {
          success: purchaseResult.success,
          message: purchaseResult.message,
        };
        if (purchaseResult.success) {
          toast.success(
            purchaseResult.message ||
              'Secondary market purchase completed successfully!',
          );
        } else {
          toast.error(
            purchaseResult.message || 'Secondary market purchase failed',
          );
        }
      } catch (error) {
        console.error('Error making secondary market purchase:', error);
        const errorMessage = formatServerError(error, t);
        toast.error(errorMessage);
        result = { success: false, message: errorMessage };
      }
    } else {
      let actionUserType: UserType;

      if (order.buyerId && currentUser.id !== order.buyerId) {
        actionUserType = UserType.BUYER;
      } else if (order.sellerId && currentUser.id !== order.sellerId) {
        actionUserType = UserType.SELLER;
      } else {
        actionUserType = effectiveUserType;
      }

      result = await executeMarketplaceOrderAction(order.id, actionUserType, t);
    }

    if (result.success && onOrderAction) {
      onOrderAction();
    }
    setActionLoading(false);
  };

  const handleShowResellHistory = () => {
    setShowResellHistory(true);
  };

  // Proposal handlers
  const handleProposePrice = () => {
    setShowPriceProposalDrawer(true);
  };

  const handleProposalCreated = () => {
    setProposalsRefreshKey((prev) => prev + 1);
    if (onOrderAction) {
      onOrderAction();
    }
  };

  const handleAcceptProposal = async (proposalId: string) => {
    if (!order?.id || !currentUser?.id) return;

    setAcceptingProposalId(proposalId);
    try {
      const result = await acceptProposal(order.id, proposalId);

      if (result.success) {
        toast.success(t(proposalsTableMessages.proposalAcceptedSuccess));
        setProposalsRefreshKey((prev) => prev + 1);
        if (onOrderAction) {
          onOrderAction();
        }
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Error accepting proposal:', error);
      toast.error(formatServerError(error, t));
    } finally {
      setAcceptingProposalId(null);
    }
  };

  const handleCancelProposal = async (proposalId: string) => {
    if (!order?.id || !currentUser?.id) return;

    setCancellingProposal(true);
    try {
      const result = await cancelProposal(order.id!, proposalId);

      if (result.success) {
        toast.success(t(proposalsTableMessages.proposalCancelledSuccess));
        setProposalsRefreshKey((prev) => prev + 1);
        if (onOrderAction) {
          onOrderAction();
        }
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Error cancelling proposal:', error);
      toast.error(formatServerError(error, t));
    } finally {
      setCancellingProposal(false);
    }
  };

  // TODO: Temporarily hidden - resell tx history feature
  const shouldShowResellHistory = false;
  // order?.secondaryMarketPrice && order.secondaryMarketPrice > 0;

  // New button logic based on requirements
  const shouldShowButton = () => {
    if (!currentUser?.id) return false;

    if (
      order.status === OrderStatus.ACTIVE &&
      order.buyerId &&
      currentUser.id !== order.buyerId
    ) {
      return true;
    }

    if (
      order.status === OrderStatus.ACTIVE &&
      order.sellerId &&
      currentUser.id !== order.sellerId
    ) {
      return true;
    }

    if (isSecondaryMarketOrder(order, currentUser)) {
      return true;
    }

    return false;
  };

  const shouldHideActionButton = hideActionButton || !shouldShowButton();

  // Check if user can propose on this order
  const canPropose = canUserProposeOnOrder(order, currentUser);
  const shouldShowProposeButton =
    userType === UserType.SELLER &&
    order.status === OrderStatus.ACTIVE &&
    !order.secondaryMarketPrice &&
    canPropose.canPropose &&
    currentUser?.id;

  const getActionLabel = () => {
    if (isSecondaryMarketOrder(order, currentUser)) {
      return (
        <div className="text-lg flex items-center gap-1">
          <span>
            {t(orderDetailsContentMessages.buy)}{' '}
            <span className="font-bold text-primary">
              {roundToThreeDecimals(order.secondaryMarketPrice)}
            </span>
          </span>
          <TonLogo className="-ml-[5px] -translate-y-[0.5px]" size={24} />
        </div>
      );
    }

    if (isActiveBuyerOrder(order, currentUser)) {
      return (
        <div className="text-lg flex items-center gap-1">
          <span>
            {t(orderDetailsContentMessages.fulfill)}{' '}
            <span className="font-bold">
              {roundToThreeDecimals(order.price)}
            </span>
          </span>
          <TonLogo className="-ml-[5px]" size={24} />
        </div>
      );
    }

    if (isActiveSellerOrder(order, currentUser)) {
      return (
        <div className="text-lg flex items-center gap-1">
          <span>
            {t(orderDetailsContentMessages.buy)}{' '}
            <span className="font-bold">
              {roundToThreeDecimals(order.price)}
            </span>
          </span>
          <TonLogo className="-ml-[5px]" size={24} />
        </div>
      );
    }

    return (
      <div className="text-lg flex items-center gap-1">
        <span>
          {t(orderDetailsContentMessages.action)}{' '}
          <span className="font-bold">{roundToThreeDecimals(order.price)}</span>
        </span>
        <TonLogo className="-ml-[5px]" size={24} />
      </div>
    );
  };

  const actionLabel = getActionLabel();

  const { gift, loading } = useOrderGift(order);

  useEffect(() => {
    if (gift) {
      preloadSingleGift(gift);
    }
  }, [gift]);

  return (
    <div className="space-y-3">
      <OrderDetailsImageSection
        collectionId={order.collectionId}
        collection={collection}
        gift={gift}
        loading={loading}
        hasGiftIdList={!!order.gift_id_list}
        bottom={
          <Button
            onClick={handleShare}
            disabled={isSharing}
            variant="outline"
            className="ml-auto border-border text-foreground hover:bg-card/50 bg-transparent rounded-2xl"
          >
            <Share className={cn('w-4 h-4', isSharing && 'animate-pulse')} />
          </Button>
        }
      />

      <OrderDetailsHeaderSection
        className="mt-2"
        collection={collection}
        order={order}
      />

      {/* <SellPriceDetails order={order} /> */}

      {gift && <OrderTraitsSection gift={gift} />}

      <OrderDetailsFeesSection order={order} />

      {/* <OrderDetailsDate updatedAt={order.updatedAt} /> */}

      {shouldShowProposalsSection(order) && userType === UserType.SELLER && (
        <OrderDetailsProposalsSection
          order={order}
          userType={effectiveUserType}
          onAcceptProposal={handleAcceptProposal}
          onCancelProposal={handleCancelProposal}
          acceptingProposalId={acceptingProposalId || undefined}
          cancellingProposal={cancellingProposal}
          proposalsRefreshKey={proposalsRefreshKey}
        />
      )}

      {role === Role.ADMIN && (
        <OrderActors
          buyerId={order.buyerId}
          sellerId={order.sellerId}
          isResellOrder={
            order.status === OrderStatus.PAID &&
            Number(order?.secondaryMarketPrice) > 0
          }
        />
      )}

      {!shouldHideActionButton ? (
        <>
          <div className="space-y-3">
            {!hasSufficientBalance && (
              <InsufficientBalance
                message={t(orderDetailsContentMessages.insufficientBalance)}
                onTopUp={handleTopUp}
              />
            )}
            {shouldShowProposeButton && (
              <Button
                onClick={handleProposePrice}
                className="w-full h-12 bg-green-500/20 border-green-500/30 text-white hover:bg-green-500/30 hover:text-green-300 rounded-2xl text-base font-medium"
              >
                <span>
                  {t(orderDetailsProposalsSectionMessages.makeProposal)}
                </span>
                <TrendingUp className="w-5 h-5 -ml-1" />
              </Button>
            )}
            <OrderDetailsActionButtons
              primaryAction={{
                label: actionLabel,
                onClick: handleAction,
                loading: actionLoading,
                disabled: !hasSufficientBalance,
              }}
              secondaryAction={
                shouldShowResellHistory
                  ? {
                      label: t(orderDetailsContentMessages.showResellHistory),
                      onClick: handleShowResellHistory,
                    }
                  : undefined
              }
              shouldShowCloseButton={showCloseButton}
              onClose={onClose}
              actionLoading={actionLoading}
            />
          </div>
        </>
      ) : showCloseButton && onClose ? (
        <div className="space-y-3 pt-3">
          <Button
            variant="destructive"
            onClick={onClose}
            className="w-full h-12 bg-red-500/20 border-red-500/30 text-white hover:bg-red-500/30 hover:text-red-300 rounded-2xl text-base font-medium flex items-center"
            disabled={actionLoading}
          >
            <span>{t(orderDetailsActionButtonsMessages.close)}</span>
            <X className="w-5 h-5 -ml-1.25 translate-y-[1px]" />
          </Button>
        </div>
      ) : null}

      {showResellHistory && order && (
        <div className="fixed inset-0 bg-black/50 z-[60] flex items-center justify-center p-4">
          <div className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <ResellTxHistory
              order={order}
              onClose={() => setShowResellHistory(false)}
            />
          </div>
        </div>
      )}

      <DepositDrawer
        open={showDepositDrawer}
        onOpenChange={setShowDepositDrawer}
      />

      {!order.secondaryMarketPrice && (
        <PriceProposalDrawer
          open={showPriceProposalDrawer}
          onOpenChange={setShowPriceProposalDrawer}
          order={order}
          onProposalCreated={handleProposalCreated}
        />
      )}
    </div>
  );
}
