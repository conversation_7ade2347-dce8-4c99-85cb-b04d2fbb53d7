import { useIntl } from 'react-intl';

import { CollectionName } from '@/components/shared/collection-name';
import { PriceLabel } from '@/components/shared/price-label';
import type { CollectionEntity, OrderEntity } from '@/marketplace-shared';
import { hasSecondaryMarketPrice } from '@/services/order-service';

import { userOrderPricingSectionMessages } from './intl/user-order-pricing-section.messages';

interface UserOrderPricingSectionProps {
  order: OrderEntity;
  collection: CollectionEntity | null;
}

export function UserOrderPricingSection({
  order,
  collection,
}: UserOrderPricingSectionProps) {
  const { formatMessage: t } = useIntl();
  const hasSecondaryPrice = hasSecondaryMarketPrice(order);

  return (
    <div className="text-center space-y-3">
      <h1 className="text-2xl font-bold text-foreground">
        <CollectionName
          collection={collection}
          fallback={`Collection ${order.collectionId}`}
        />
      </h1>

      {hasSecondaryPrice ? (
        <div className="space-y-3">
          <div>
            <p className="text-muted-foreground text-sm mb-1">
              {t(userOrderPricingSectionMessages.primaryPrice)}
            </p>
            <div className="flex items-center justify-center gap-2 p-2 bg-card rounded-lg">
              <PriceLabel
                amount={order.price}
                size={20}
                className="text-xl font-semibold text-foreground"
                showUnit
              />
            </div>
          </div>

          <div>
            <p className="text-primary text-sm mb-1">
              {t(userOrderPricingSectionMessages.secondaryMarketPrice)}
            </p>
            <div className="flex items-center justify-center gap-2 p-3 bg-background rounded-lg border border-primary/20">
              <PriceLabel
                amount={order.secondaryMarketPrice || 0}
                size={24}
                className="text-3xl font-bold text-primary"
                showUnit
              />
            </div>
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-center gap-2 p-3 bg-background rounded-lg">
          <PriceLabel
            amount={order.price}
            size={24}
            className="text-3xl font-bold text-foreground"
            showUnit
          />
        </div>
      )}
    </div>
  );
}
