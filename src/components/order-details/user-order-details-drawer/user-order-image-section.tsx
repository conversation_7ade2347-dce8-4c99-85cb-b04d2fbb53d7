import { TgsOrImage } from '@/components/tgs/tgs-or-image';
import { TgsOrImageGift } from '@/components/tgs/tgs-or-image-gift';
import { TgsSkeleton } from '@/components/tgs/tgs-skeleton';
import { useOrderGift } from '@/hooks/use-order-gift';
import { cn } from '@/lib/utils';
import type { CollectionEntity, OrderEntity } from '@/marketplace-shared';

interface UserOrderImageSectionProps {
  collection: CollectionEntity | null;
  order: OrderEntity;
  top?: React.ReactNode;
  bottom?: React.ReactNode;
}

export function UserOrderImageSection({
  collection,
  order,
  bottom,
  top,
}: UserOrderImageSectionProps) {
  const { gift, loading } = useOrderGift(order);

  // Only show skeleton if we're actively loading AND expect a gift but don't have it yet
  // If we have gift data OR we know there's no gift, show content immediately
  const shouldShowSkeleton = loading && order.gift_id_list && !gift;

  return (
    <div className="relative">
      <div
        className={cn(
          'aspect-square relative rounded-2xl overflow-hidden bg-gradient-to-br from-card to-background border border-border/50 p-8',
          gift && 'p-0',
        )}
      >
        {top && (
          <div className="w-full flex absolute top-0 left-0 px-2 pt-2">
            {top}
          </div>
        )}
        {shouldShowSkeleton ? (
          <TgsSkeleton
            className="w-full h-full"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
            }}
          />
        ) : gift ? (
          <TgsOrImageGift
            isImage={false}
            gift={gift}
            className="w-full h-full"
            style={{ width: '100%', height: '100%' }}
          />
        ) : collection ? (
          <TgsOrImage
            isImage={false}
            collectionId={collection.id}
            imageProps={{
              alt: collection.name || 'Order item',
              fill: true,
              className: 'object-contain drop-shadow-2xl',
            }}
            tgsProps={{
              style: { height: '100%', width: '100%' },
            }}
          />
        ) : (
          <div className="w-full h-full bg-border rounded flex items-center justify-center">
            <div className="w-16 h-16 bg-background rounded" />
          </div>
        )}
        {bottom && (
          <div className="w-full flex absolute bottom-0 left-0">{bottom}</div>
        )}
      </div>
    </div>
  );
}
