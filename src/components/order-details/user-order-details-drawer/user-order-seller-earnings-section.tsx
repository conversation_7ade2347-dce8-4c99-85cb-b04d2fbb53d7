'use client';

import { useState } from 'react';
import { useIntl } from 'react-intl';

import { CollapsibleSection } from '@/components/shared/collapsible-section';
import { PriceLabel } from '@/components/shared/price-label';
import type { OrderEntity } from '@/marketplace-shared';

import { userOrderSellerEarningsSectionMessages } from './intl/user-order-seller-earnings-section.messages';

interface UserOrderSellerEarningsSectionProps {
  order: OrderEntity;
}

export function UserOrderSellerEarningsSection({
  order,
}: UserOrderSellerEarningsSectionProps) {
  const { formatMessage: t } = useIntl();
  const [isOpen, setIsOpen] = useState(false);

  if (
    !order.reseller_earnings_for_seller ||
    order.reseller_earnings_for_seller <= 0
  ) {
    return null;
  }

  return (
    <CollapsibleSection
      title={t(userOrderSellerEarningsSectionMessages.resaleEarnings)}
      defaultOpen={false}
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <div className="bg-[var(--color-status-green-bg)] border border-[var(--color-status-green-border)] rounded-lg p-4">
        <div className="flex items-center justify-between">
          <span className="text-[var(--color-status-green)] text-sm font-medium">
            {t(userOrderSellerEarningsSectionMessages.totalEarningsFromResales)}
          </span>
          <PriceLabel
            amount={order.reseller_earnings_for_seller || 0}
            size={20}
            className="text-xl font-bold text-[var(--color-status-green)]"
            showUnit
          />
        </div>
        <p className="text-xs text-[var(--color-status-green)]/80 mt-2">
          {t(userOrderSellerEarningsSectionMessages.earningsDescription)}
        </p>
      </div>
    </CollapsibleSection>
  );
}
