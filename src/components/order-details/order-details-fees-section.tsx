'use client';

import { Check } from 'lucide-react';
import { useState } from 'react';
import { useIntl } from 'react-intl';

import { CollapsibleSection } from '@/components/shared/collapsible-section';
import { PriceLabel } from '@/components/shared/price-label';
import type { OrderEntity } from '@/marketplace-shared';
import { OrderStatus } from '@/marketplace-shared';
import {
  calculateOrderAmounts,
  formatBPSToPercent,
  getOrderCollateral,
} from '@/services/order-service';

import { orderDetailsFeesMessages } from './intl/order-details-fees-section.messages';

interface OrderDetailsFeesSection {
  order: OrderEntity;
}

export function OrderDetailsFeesSection({ order }: OrderDetailsFeesSection) {
  const { formatMessage: t } = useIntl();
  const [isOpen, setIsOpen] = useState(false);

  const amounts = calculateOrderAmounts(order, true, false);
  const feePercent = formatBPSToPercent(order.fees?.purchase_fee || 500);
  const buyerPercentageZeroDecimal = formatBPSToPercent(
    order.fees?.buyer_locked_percentage || 0,
    0,
  );
  const sellerPercentageZeroDecimal = formatBPSToPercent(
    order.fees?.seller_locked_percentage || 0,
    0,
  );

  const collateralAmount = getOrderCollateral(order, amounts);

  // Check if order has gift and status is not created, fulfilled, or cancelled
  const shouldShowGiftText =
    order.gift_id_list &&
    order.status !== OrderStatus.CREATED &&
    order.status !== OrderStatus.FULFILLED &&
    order.status !== OrderStatus.CANCELLED;

  return (
    <CollapsibleSection
      title={t(orderDetailsFeesMessages.orderDetailsAndFees)}
      defaultOpen={true}
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-foreground font-medium">
            {t(orderDetailsFeesMessages.purchaseFee)}
          </span>
          <PriceLabel
            amount={amounts.purchaseFeeAmount}
            size={16}
            className="text-primary font-semibold"
            showUnit
          />
        </div>
        <p className="text-xs text-muted-foreground">
          {t(orderDetailsFeesMessages.feePaidBySeller, { feePercent })}
        </p>
      </div>

      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-foreground font-medium">
            {t(orderDetailsFeesMessages.collateral)}
          </span>
          <PriceLabel
            amount={collateralAmount}
            size={16}
            className="text-primary font-semibold"
            showUnit
          />
        </div>

        <div>
          <div className="flex items-center gap-1">
            <span className="text-muted-foreground text-xs">
              {t(orderDetailsFeesMessages.buyer)} - {buyerPercentageZeroDecimal}
              % ({amounts.buyerLockedAmount.toFixed(1)} TON)
            </span>
            {order?.buyerId && (
              <div className="text-[var(--color-status-green)] flex items-center text-[10px]">
                <span>{t(orderDetailsFeesMessages.deposited)}</span>
                <Check className="h-3" />
              </div>
            )}
          </div>
          <div className="flex items-center gap-1">
            <span className="text-muted-foreground text-xs">
              {t(orderDetailsFeesMessages.seller)} -{' '}
              {shouldShowGiftText
                ? t(orderDetailsFeesMessages.gift)
                : `${sellerPercentageZeroDecimal}% (${amounts.sellerLockedAmount.toFixed(1)} TON)`}
            </span>
            {order?.sellerId && (
              <div className="text-[var(--color-status-green)] flex items-center text-[10px]">
                <span>{t(orderDetailsFeesMessages.deposited)}</span>
                <Check className="h-3" />
              </div>
            )}
          </div>
        </div>

        <p className="text-xs text-muted-foreground leading-relaxed">
          {t(orderDetailsFeesMessages.collateralDescription, {
            buyerPercentage: buyerPercentageZeroDecimal,
          })}
        </p>
      </div>
    </CollapsibleSection>
  );
}
