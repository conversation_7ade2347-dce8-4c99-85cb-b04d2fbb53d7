'use client';

import { Button as TgButton } from '@telegram-apps/telegram-ui';
import { ChevronDown, LogOut } from 'lucide-react';
import { useIntl } from 'react-intl';

import { GlassWrapper } from '@/components/shared/glass-wrapper';
import { TonLogo } from '@/components/TonLogo';
import { useTgUiStyles } from '@/hooks/use-tg-ui-styles';
import { useThemeSwitcher } from '@/hooks/use-theme-switcher';
import { cn } from '@/lib/utils';

import { tonConnectButtonMessages } from './intl/ton-connect-button.messages';

interface TonConnectButtonProps {
  tonWalletAddress: string;
  isConnecting: boolean;
  isAuthenticating: boolean;
  showWalletDropdown?: boolean;
  setShowWalletDropdown?: (show: boolean) => void;
  dropdownRef?: React.RefObject<HTMLDivElement | null>;
  onWalletAction: () => Promise<string | void>;
  onDisconnectWallet: () => Promise<void>;
  formatAddress: (address: string) => string;
  className?: string;
  size?: 'default' | 'compact';
  showDropdown?: boolean;
}

export function TonConnectButton({
  tonWalletAddress,
  isConnecting,
  isAuthenticating,
  showWalletDropdown = false,
  setShowWalletDropdown,
  dropdownRef,
  onWalletAction,
  onDisconnectWallet,
  formatAddress,
  className = '',
  size = 'default',
  showDropdown = true,
}: TonConnectButtonProps) {
  const { formatMessage: t } = useIntl();
  const { theme } = useThemeSwitcher();
  const tgUiStyles = useTgUiStyles();

  const handleWalletClick = async () => {
    const result = await onWalletAction();
    if (result === 'show-dropdown' && setShowWalletDropdown) {
      setShowWalletDropdown(!showWalletDropdown);
    }
  };

  const handleDisconnect = async () => {
    await onDisconnectWallet();
    if (setShowWalletDropdown) {
      setShowWalletDropdown(false);
    }
  };

  const isCompact = size === 'compact';
  const buttonHeight = isCompact ? 'h-8' : 'h-10';
  const logoSize = isCompact ? 20 : 24;
  const textSize = isCompact ? 'text-xs xs:text-sm' : 'text-sm xs:text-base';

  // Get theme-aware button colors and styles
  const getButtonStyles = () => {
    const baseStyles = {
      backgroundColor: tgUiStyles['--tgui--button_color'],
      color: tgUiStyles['--tgui--button_text_color'],
      border: 'none',
      boxShadow:
        theme === 'black'
          ? '0 2px 8px rgba(218, 165, 32, 0.3)' // Gold glow for black theme
          : theme === 'dark'
            ? '0 2px 8px rgba(106, 178, 242, 0.2)' // Blue glow for dark theme
            : '0 1px 3px rgba(0, 122, 255, 0.2)', // Subtle shadow for light theme
    };

    return baseStyles;
  };

  // Get theme-aware text color for better contrast
  const getTextColor = () => {
    return {
      color: tgUiStyles['--tgui--button_text_color'],
    };
  };

  const connectButtonWidth = 'w-[130px] xss:w-[155px] xs:w-[170px]';

  return (
    <div
      className={cn('relative', connectButtonWidth, className)}
      ref={dropdownRef}
    >
      <TgButton
        className={cn(
          '[&>h6]:flex [&>h6]:items-center [&>h6]:gap-1',
          'rounded-full!',
          buttonHeight,
          'w-full',
          'px-3!',
          'transition-all duration-200',
          'hover:opacity-90 active:scale-95',
          // Remove hardcoded bg-primary and use theme-aware styles instead
        )}
        style={getButtonStyles()}
        onClick={handleWalletClick}
        disabled={isConnecting || isAuthenticating}
      >
        <TonLogo size={logoSize} className="-mr-1" />
        <span
          className={cn('truncate font-medium', textSize)}
          style={getTextColor()}
        >
          {isAuthenticating
            ? t(tonConnectButtonMessages.authenticating)
            : isConnecting
              ? t(tonConnectButtonMessages.connecting)
              : tonWalletAddress
                ? formatAddress(tonWalletAddress)
                : t(tonConnectButtonMessages.connect)}
        </span>
        {tonWalletAddress && showDropdown && (
          <ChevronDown className="w-3 h-3 ml-1" style={getTextColor()} />
        )}
      </TgButton>
      {showWalletDropdown && tonWalletAddress && showDropdown && (
        <div
          className={cn(
            'absolute right-0 top-full mt-1 z-100',
            connectButtonWidth,
          )}
        >
          <GlassWrapper
            variant="default"
            intensity="light"
            className="bg-background/30 border-b border-border/50 p-0 w-full [&_div]:rounded-full overflow-hidden"
            enableHover={false}
          >
            <button
              onClick={handleDisconnect}
              className={cn(
                'w-full px-3 py-2 text-left text-sm text-foreground flex justify-center items-center',
                'hover:text-foreground flex items-center gap-2',
                'transition-all duration-200',
                'hover:bg-destructive/10 hover:text-destructive',
                'focus:outline-none focus:bg-destructive/10 focus:text-destructive',
              )}
            >
              <LogOut className="w-3 h-3" />
              {t(tonConnectButtonMessages.disconnect)}
            </button>
          </GlassWrapper>
        </div>
      )}
    </div>
  );
}
