'use client';

import { useState } from 'react';
import { useIntl } from 'react-intl';

import { CollapsibleSection } from '@/components/shared/collapsible-section';
import type { OrderGift } from '@/marketplace-shared';

import { orderTraitsSectionMessages } from './intl/order-traits-section.messages';

interface OrderTraitsSectionProps {
  gift: OrderGift;
}

interface TraitRowProps {
  name: string;
  value: string;
  rarity: string;
}

function TraitRow({ name, value, rarity }: TraitRowProps) {
  return (
    <div className="flex justify-between items-center py-2">
      <span className="text-muted-foreground text-sm capitalize">{name}</span>
      <span className="text-foreground text-sm font-medium">
        {value} <span className="text-primary">({rarity})</span>
      </span>
    </div>
  );
}

export function OrderTraitsSection({ gift }: OrderTraitsSectionProps) {
  const { formatMessage: t } = useIntl();
  const [isOpen, setIsOpen] = useState(false);

  const formatRarity = (rarityPerMille: number): string => {
    const percentage = (rarityPerMille / 10).toFixed(1);
    return `${percentage}%`;
  };

  const traits = [
    {
      name: 'backdrop',
      value: gift.backdrop.name,
      rarity: formatRarity(gift.backdrop.rarity_per_mille),
    },
    {
      name: 'model',
      value: gift.model.name,
      rarity: formatRarity(gift.model.rarity_per_mille),
    },
    {
      name: 'symbol',
      value: gift.symbol.name,
      rarity: formatRarity(gift.symbol.rarity_per_mille),
    },
  ];

  return (
    <CollapsibleSection
      title={t(orderTraitsSectionMessages.giftTraits)}
      defaultOpen={false}
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <div className="bg-card border border-border rounded-lg p-4">
        <div className="space-y-1">
          {traits.map((trait) => (
            <TraitRow
              key={trait.name}
              name={trait.name}
              value={trait.value}
              rarity={trait.rarity}
            />
          ))}
        </div>
      </div>
    </CollapsibleSection>
  );
}
