import { useIntl } from 'react-intl';

import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import type { OrderEntity } from '@/marketplace-shared';
import { OrderStatus } from '@/marketplace-shared';
import { getStatusConfig } from '@/services/order-service';

import { orderStatusMessages } from './order-status-badge.messages';

interface OrderStatusBadgeProps {
  order: OrderEntity;
  variant?: 'default' | 'pill';
  className?: string;
}

interface StatusBadgeProps {
  status: OrderStatus;
  variant?: 'default' | 'pill';
  className?: string;
}

const statusLabelMap = {
  [OrderStatus.CREATED]: orderStatusMessages.created,
  [OrderStatus.ACTIVE]: orderStatusMessages.active,
  [OrderStatus.PAID]: orderStatusMessages.paid,
  [OrderStatus.GIFT_SENT_TO_RELAYER]: orderStatusMessages.giftSentToRelayer,
  [OrderStatus.FULFILLED]: orderStatusMessages.fulfilled,
  [OrderStatus.CANCELLED]: orderStatusMessages.cancelled,
};

export function OrderStatusBadge({
  order,
  variant = 'default',
  className,
}: OrderStatusBadgeProps) {
  return (
    <StatusBadge
      status={order.status}
      variant={variant}
      className={className}
    />
  );
}

export function StatusBadge({
  status,
  variant = 'default',
  className,
}: StatusBadgeProps) {
  const { formatMessage: t } = useIntl();
  const statusConfig = getStatusConfig(status);

  const getStatusLabel = (status: OrderStatus): string => {
    const labelFunction = t(statusLabelMap[status]);
    return labelFunction || statusConfig.label;
  };

  const baseClasses =
    variant === 'pill' ? 'text-[10px] px-2 py-0.5 border' : '';

  return (
    <Badge
      variant="outline"
      className={cn(baseClasses, statusConfig.className, className)}
    >
      {getStatusLabel(status)}
    </Badge>
  );
}
