'use client';

import { forwardRef } from 'react';

import { cn } from '@/lib/utils';

interface GlassWrapperProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  variant?: 'default' | 'footer' | 'image';
  intensity?: 'light' | 'medium' | 'strong';
  enableHover?: boolean;
}

const GlassWrapper = forwardRef<HTMLDivElement, GlassWrapperProps>(
  (
    {
      children,
      className,
      variant = 'default',
      intensity = 'medium',
      enableHover = true,
      ...props
    },
    ref,
  ) => {
    const getWrapperClasses = () => {
      const baseClasses =
        'relative flex font-semibold transition-all duration-500 ease-out';

      const shadowClasses = {
        light: 'shadow-lg shadow-black/20',
        medium:
          'shadow-[0_8px_32px_rgba(0,0,0,0.3),0_0_40px_rgba(0,0,0,0.2),0_2px_8px_rgba(0,0,0,0.4)]',
        strong:
          'shadow-[0_12px_48px_rgba(0,0,0,0.4),0_0_60px_rgba(0,0,0,0.3),0_4px_16px_rgba(0,0,0,0.5)]',
      };

      const hoverClasses = enableHover
        ? 'hover:shadow-[0_12px_48px_rgba(0,0,0,0.4),0_0_60px_rgba(0,0,0,0.3),0_4px_16px_rgba(0,0,0,0.5)] hover:-translate-y-0.5 hover:scale-[1.02]'
        : '';

      const variantClasses = {
        default: 'rounded-xl p-4',
        footer:
          'rounded-[1.2rem] p-1.5 min-h-[60px] hover:p-2 hover:rounded-[1.75rem] rounded-[40px]',
        image: 'rounded-lg p-2',
      };

      return cn(
        baseClasses,
        shadowClasses[intensity],
        hoverClasses,
        variantClasses[variant],
        className,
      );
    };

    const getEffectClasses = () => {
      const intensityClasses = {
        light: 'backdrop-blur-[2px] saturate-150',
        medium: 'backdrop-blur-[4px] saturate-[180%]',
        strong: 'backdrop-blur-[6px] saturate-[200%]',
      };

      return cn('absolute inset-0 z-0', intensityClasses[intensity]);
    };

    const getTintClasses = () => {
      const intensityClasses = {
        light: 'bg-gradient-to-br from-white/10 via-transparent to-white/5',
        medium: 'bg-gradient-to-br from-white/15 via-white/3 to-white/8',
        strong: 'bg-gradient-to-br from-white/20 via-white/5 to-white/12',
      };

      // For footer variant, use theme-aware tints
      if (variant === 'footer') {
        return cn(
          'absolute inset-0 z-10',
          'bg-gradient-to-t from-background/20 via-background/10 to-transparent',
          'dark:from-card/30 dark:via-card/15 dark:to-transparent',
          'black:from-card/40 black:via-card/20 black:to-transparent',
        );
      }

      return cn('absolute inset-0 z-10', intensityClasses[intensity]);
    };

    const getShineClasses = () => {
      return 'absolute inset-0 z-20';
    };

    const getContentClasses = () => {
      // For footer variant, use theme-aware text colors
      if (variant === 'footer') {
        return 'relative z-30 w-full text-foreground';
      }

      return 'relative z-30 text-white w-full';
    };

    return (
      <>
        <svg className="hidden">
          <filter
            id="glass-distortion"
            x="0%"
            y="0%"
            width="100%"
            height="100%"
            filterUnits="objectBoundingBox"
          >
            <feTurbulence
              type="fractalNoise"
              baseFrequency="0.01 0.01"
              numOctaves="1"
              seed="5"
              result="turbulence"
            />
            <feComponentTransfer in="turbulence" result="mapped">
              <feFuncR type="gamma" amplitude="1" exponent="10" offset="0.5" />
              <feFuncG type="gamma" amplitude="0" exponent="1" offset="0" />
              <feFuncB type="gamma" amplitude="0" exponent="1" offset="0.5" />
            </feComponentTransfer>
            <feGaussianBlur
              in="turbulence"
              stdDeviation="1.5"
              result="softMap"
            />
            <feSpecularLighting
              in="softMap"
              surfaceScale="5"
              specularConstant="1"
              specularExponent="100"
              lightingColor="white"
              result="specLight"
            >
              <fePointLight x="-200" y="-200" z="300" />
            </feSpecularLighting>
            <feComposite
              in="specLight"
              operator="arithmetic"
              k1="0"
              k2="1"
              k3="1"
              k4="0"
              result="litImage"
            />
            <feDisplacementMap
              in="SourceGraphic"
              in2="softMap"
              scale="150"
              xChannelSelector="R"
              yChannelSelector="G"
            />
          </filter>
        </svg>

        <div ref={ref} className={getWrapperClasses()} {...props}>
          <div
            className={getEffectClasses()}
            style={{ filter: 'url(#glass-distortion)' }}
          />

          <div className={getTintClasses()} />

          <div className={getShineClasses()} />

          <div className={getContentClasses()}>{children}</div>
        </div>
      </>
    );
  },
);

GlassWrapper.displayName = 'GlassWrapper';

export { GlassWrapper };
