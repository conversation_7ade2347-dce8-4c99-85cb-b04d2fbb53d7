'use client';

import { OrderGiftImage } from '@/components/shared/order-gift-image';
import type { CollectionEntity, OrderEntity } from '@/marketplace-shared';

interface OrderImageProps {
  order: OrderEntity;
  collection: CollectionEntity | null | undefined;
  className?: string;
  children?: React.ReactNode;
  badge?: React.ReactNode;
}

export function OrderImage({
  order,
  collection,
  className,
  children,
  badge,
}: OrderImageProps) {
  return (
    <OrderGiftImage
      order={order}
      collection={collection}
      className={className}
      badge={badge}
    >
      {children}
    </OrderGiftImage>
  );
}
