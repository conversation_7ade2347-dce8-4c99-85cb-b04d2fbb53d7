'use client';

import { ChevronDown } from 'lucide-react';
import type { ReactNode } from 'react';
import { useState } from 'react';

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { cn } from '@/lib/utils';

interface CollapsibleSectionProps {
  title: string;
  children: ReactNode;
  defaultOpen?: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  triggerClassName?: string;
  contentClassName?: string;
  className?: string;
  icon?: ReactNode;
}

export function CollapsibleSection({
  title,
  children,
  defaultOpen = false,
  open: controlledOpen,
  onOpenChange: controlledOnOpenChange,
  triggerClassName,
  contentClassName,
  className,
  icon,
}: CollapsibleSectionProps) {
  const [internalOpen, setInternalOpen] = useState(defaultOpen);

  // Use controlled state if provided, otherwise use internal state
  const isOpen = controlledOpen !== undefined ? controlledOpen : internalOpen;
  const handleOpenChange = controlledOnOpenChange || setInternalOpen;

  return (
    <div className="space-y-4">
      <Collapsible
        className={cn('bg-card/80 rounded-lg', className)}
        defaultOpen={defaultOpen}
        open={isOpen}
        onOpenChange={handleOpenChange}
      >
        <CollapsibleTrigger
          className={cn(
            'flex items-center justify-between w-full p-4 rounded-lg transition-colors duration-200',
            triggerClassName,
          )}
        >
          <div className="flex items-center gap-3">
            {icon}
            <span className="text-foreground font-medium">{title}</span>
          </div>
          <ChevronDown
            className={cn(
              'h-4 w-4 text-muted-foreground transition-transform duration-300 ease-in-out',
              isOpen && 'rotate-180',
            )}
          />
        </CollapsibleTrigger>

        <CollapsibleContent className="overflow-hidden transition-all duration-300 ease-in-out data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down">
          <div
            className={cn('px-4 pb-4 space-y-4 rounded-lg', contentClassName)}
          >
            {children}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
