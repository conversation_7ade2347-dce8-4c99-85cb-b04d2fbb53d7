'use client';

import { Button } from '@telegram-apps/telegram-ui';

import { PriceLabel } from '@/components/shared/price-label';
import { roundToThreeDecimals } from '@/lib/utils';
import type { OrderEntity } from '@/marketplace-shared';

interface SellButtonComponentProps {
  order: OrderEntity;
  className?: string;
  tonLogoClassName?: string;
}

export function SellButtonComponent({
  order,
  className = '',
  tonLogoClassName = '',
}: SellButtonComponentProps) {
  // const defaultLabel = label || t(sellButtonComponentMessages.buy);
  const hasSecondaryPrice =
    order.secondaryMarketPrice && order.secondaryMarketPrice > 0;
  const currentPrice = hasSecondaryPrice
    ? order.secondaryMarketPrice
    : order.price;

  return (
    <Button
      className={`w-full [&>h6]:flex [&>h6]:items-center [&>h6]:justify-center [&>h6]:gap-1 ${className}`}
    >
      <div className="flex items-center gap-1">
        {/* {defaultLabel && <span>{defaultLabel}</span>} */}
        <PriceLabel
          amount={roundToThreeDecimals(currentPrice)}
          size={24}
          tonLogoClassName={`-ml-1 ${tonLogoClassName}`}
          className="text-lg font-bold"
          clickable={false}
        />
      </div>
    </Button>
  );
}
