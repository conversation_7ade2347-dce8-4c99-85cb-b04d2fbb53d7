'use client';

import { Gift, Package, RefreshCw, Trash2 } from 'lucide-react';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useThemeSwitcher } from '@/hooks/use-theme-switcher';

/**
 * Demo component showing user order card buttons with theme-aware styling
 * Demonstrates how buttons adapt to different themes, especially the gold color in black theme
 */
export function UserOrderButtonsDemo() {
  const { theme, getThemeLabel } = useThemeSwitcher();

  // Button configurations matching the user order card
  const buttonConfigs = [
    {
      name: 'Send Gift',
      icon: Gift,
      className:
        'bg-[var(--color-status-purple)] hover:bg-[var(--color-status-purple)]/90 text-white shadow-sm hover:shadow-md transition-all duration-200 black:shadow-[0_2px_8px_rgba(168,85,247,0.3)] dark:shadow-[0_2px_8px_rgba(168,85,247,0.2)] black:bg-[var(--color-tg-button)] black:text-[var(--color-tg-button-text)] black:hover:bg-[var(--color-tg-button)]/90',
      description: 'Purple → Gold in black theme',
    },
    {
      name: 'Get Gift',
      icon: Package,
      className:
        'bg-[var(--color-status-purple)] hover:bg-[var(--color-status-purple)]/90 text-white shadow-sm hover:shadow-md transition-all duration-200 black:shadow-[0_2px_8px_rgba(168,85,247,0.3)] dark:shadow-[0_2px_8px_rgba(168,85,247,0.2)] black:bg-[var(--color-tg-button)] black:text-[var(--color-tg-button-text)] black:hover:bg-[var(--color-tg-button)]/90',
      description: 'Purple → Gold in black theme',
    },
    {
      name: 'Resell Order',
      icon: RefreshCw,
      className:
        'bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm hover:shadow-md transition-all duration-200 black:shadow-[0_2px_8px_rgba(218,165,32,0.3)] dark:shadow-[0_2px_8px_rgba(106,178,242,0.2)] black:bg-[var(--color-tg-button)] black:text-[var(--color-tg-button-text)] black:hover:bg-[var(--color-tg-button)]/90',
      description: 'Primary → Gold in black theme',
    },
    {
      name: 'Attach Gift',
      icon: Gift,
      className:
        'bg-[var(--color-status-green)] hover:bg-[var(--color-status-green)]/90 text-white shadow-sm hover:shadow-md transition-all duration-200 black:shadow-[0_2px_8px_rgba(74,222,128,0.3)] dark:shadow-[0_2px_8px_rgba(74,222,128,0.2)] black:bg-[var(--color-tg-button)] black:text-[var(--color-tg-button-text)] black:hover:bg-[var(--color-tg-button)]/90',
      description: 'Green → Gold in black theme',
    },
    {
      name: 'Get Cancelled',
      icon: Trash2,
      className:
        'bg-[var(--color-status-red)] hover:bg-[var(--color-status-red)]/90 text-white shadow-sm hover:shadow-md transition-all duration-200 black:shadow-[0_2px_8px_rgba(255,68,68,0.3)] dark:shadow-[0_2px_8px_rgba(236,57,66,0.2)] black:bg-destructive black:text-destructive-foreground black:hover:bg-destructive/90',
      description: 'Red → Red (destructive) in black theme',
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>User Order Card Buttons - Theme Aware</CardTitle>
        <p className="text-sm text-muted-foreground">
          Current theme:{' '}
          <span className="font-semibold">{getThemeLabel(theme)}</span>
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Button Examples */}
        <div>
          <h4 className="font-medium mb-3">Order Action Buttons</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {buttonConfigs.map((config) => {
              const IconComponent = config.icon;
              return (
                <div key={config.name} className="space-y-2">
                  <button
                    className={`inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium px-4 py-2 w-full ${config.className}`}
                  >
                    <IconComponent className="w-4 h-4" />
                    {config.name}
                  </button>
                  <p className="text-xs text-muted-foreground text-center">
                    {config.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>

        {/* Theme Behavior Explanation */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="p-3 rounded-lg bg-muted/50">
            <h5 className="font-medium mb-2">☀️ Light Theme</h5>
            <ul className="text-xs text-muted-foreground space-y-1">
              <li>• Purple for gift actions</li>
              <li>• Blue for primary actions</li>
              <li>• Green for attach actions</li>
              <li>• Red for destructive actions</li>
              <li>• Clean shadows</li>
            </ul>
          </div>
          <div className="p-3 rounded-lg bg-muted/50">
            <h5 className="font-medium mb-2">🌙 Dark Theme</h5>
            <ul className="text-xs text-muted-foreground space-y-1">
              <li>• Same color scheme</li>
              <li>• Muted appearance</li>
              <li>• Subtle glow effects</li>
              <li>• Better for dark environments</li>
              <li>• Telegram-style aesthetics</li>
            </ul>
          </div>
          <div className="p-3 rounded-lg bg-muted/50">
            <h5 className="font-medium mb-2">⚫ Black Theme</h5>
            <ul className="text-xs text-muted-foreground space-y-1">
              <li>
                • <strong>Gold color for most actions</strong> ✨
              </li>
              <li>• Red stays red (destructive)</li>
              <li>• Golden glow effects</li>
              <li>• Premium appearance</li>
              <li>• OLED-friendly design</li>
            </ul>
          </div>
        </div>

        {/* Technical Details */}
        <div className="text-xs text-muted-foreground space-y-2">
          <p>
            <strong>Black Theme Behavior:</strong> Most buttons use the gold
            color (<code>--color-tg-button</code>) for a consistent, premium
            appearance. Only destructive actions (like &quot;Get
            Cancelled&quot;) maintain their red color for safety.
          </p>
          <p>
            <strong>Glow Effects:</strong> Each theme has specific shadow/glow
            effects that match the button colors, creating depth and visual
            interest while maintaining accessibility.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
