'use client';

import { Download, Heart, Plus, <PERSON>ting<PERSON>, Star, Trash2 } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useThemeSwitcher } from '@/hooks/use-theme-switcher';

/**
 * Comprehensive demo component showing all button variants
 * with theme-aware styling and interactive states
 */
export function ButtonVariantsDemo() {
  const { theme, getThemeLabel } = useThemeSwitcher();

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Interactive Button States</CardTitle>
          <p className="text-sm text-muted-foreground">
            Current theme:{' '}
            <span className="font-semibold">{getThemeLabel(theme)}</span>
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Action Buttons */}
          <div>
            <h4 className="font-medium mb-3">Action Buttons</h4>
            <div className="flex flex-wrap gap-3">
              <Button variant="default">
                <Plus className="w-4 h-4" />
                Create Order
              </Button>
              <Button variant="success">
                <Download className="w-4 h-4" />
                Download
              </Button>
              <Button variant="warning">
                <Star className="w-4 h-4" />
                Favorite
              </Button>
              <Button variant="destructive">
                <Trash2 className="w-4 h-4" />
                Delete
              </Button>
            </div>
          </div>

          {/* TON-Specific Buttons */}
          <div>
            <h4 className="font-medium mb-3">TON-Specific Actions</h4>
            <div className="flex flex-wrap gap-3">
              <Button variant="ton">Connect Wallet</Button>
              <Button variant="ton" size="lg">
                <Plus className="w-4 h-4" />
                Buy TON
              </Button>
              <Button variant="outline">View on TONScan</Button>
            </div>
          </div>

          {/* Button States */}
          <div>
            <h4 className="font-medium mb-3">Button States</h4>
            <div className="flex flex-wrap gap-3">
              <Button variant="default">Normal</Button>
              <Button variant="default" disabled>
                Disabled
              </Button>
              <Button variant="outline">
                <Settings className="w-4 h-4 animate-spin" />
                Loading
              </Button>
              <Button variant="ghost">
                <Heart className="w-4 h-4 text-red-500" />
                Liked
              </Button>
            </div>
          </div>

          {/* Size Comparison */}
          <div>
            <h4 className="font-medium mb-3">Size Comparison</h4>
            <div className="flex flex-wrap items-end gap-3">
              <Button size="sm" variant="outline">
                Small
              </Button>
              <Button size="default" variant="default">
                Default
              </Button>
              <Button size="lg" variant="secondary">
                Large
              </Button>
              <Button size="xl" variant="success">
                Extra Large
              </Button>
            </div>
          </div>

          {/* Icon-Only Buttons */}
          <div>
            <h4 className="font-medium mb-3">Icon-Only Buttons</h4>
            <div className="flex flex-wrap items-center gap-3">
              <Button size="icon-sm" variant="ghost">
                <Heart className="w-3 h-3" />
              </Button>
              <Button size="icon" variant="outline">
                <Settings className="w-4 h-4" />
              </Button>
              <Button size="icon-lg" variant="default">
                <Plus className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Theme-Specific Features</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="p-3 rounded-lg bg-muted/50">
              <h5 className="font-medium mb-2">☀️ Light Theme</h5>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Clean iOS-style shadows</li>
                <li>• Bright, vibrant colors</li>
                <li>• High contrast text</li>
                <li>• Subtle hover effects</li>
              </ul>
            </div>
            <div className="p-3 rounded-lg bg-muted/50">
              <h5 className="font-medium mb-2">🌙 Dark Theme</h5>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Muted color palette</li>
                <li>• Subtle glow effects</li>
                <li>• Telegram-style aesthetics</li>
                <li>• Reduced eye strain</li>
              </ul>
            </div>
            <div className="p-3 rounded-lg bg-muted/50">
              <h5 className="font-medium mb-2">⚫ Black Theme</h5>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Bright, vibrant colors</li>
                <li>• Golden glow effects ✨</li>
                <li>• OLED-friendly design</li>
                <li>• Premium appearance</li>
              </ul>
            </div>
          </div>

          <div className="text-xs text-muted-foreground">
            <p>
              <strong>Note:</strong> All buttons automatically adapt their
              colors, shadows, and glow effects based on the current theme. The
              black theme features special golden glows for a premium feel.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
