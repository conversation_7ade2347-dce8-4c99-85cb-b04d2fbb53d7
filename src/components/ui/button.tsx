import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import * as React from 'react';

import { cn } from '@/lib/utils';

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 active:scale-95",
  {
    variants: {
      variant: {
        default:
          'bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:shadow-md focus-visible:ring-primary/50 black:shadow-[0_2px_8px_rgba(218,165,32,0.3)] dark:shadow-[0_2px_8px_rgba(106,178,242,0.2)]',
        destructive:
          'bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90 hover:shadow-md focus-visible:ring-destructive/50 black:shadow-[0_2px_8px_rgba(255,68,68,0.3)] dark:shadow-[0_2px_8px_rgba(236,57,66,0.2)]',
        outline:
          'border border-border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:shadow-md focus-visible:ring-accent/50 black:border-border/50 black:hover:bg-accent/20 dark:hover:bg-accent/10',
        secondary:
          'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:shadow-md focus-visible:ring-secondary/50 black:shadow-[0_1px_4px_rgba(255,255,255,0.1)] dark:shadow-[0_1px_4px_rgba(35,46,60,0.3)]',
        ghost:
          'hover:bg-accent hover:text-accent-foreground focus-visible:ring-accent/50 black:hover:bg-accent/10 dark:hover:bg-accent/20',
        link: 'text-primary underline-offset-4 hover:underline focus-visible:ring-primary/50 black:text-[var(--color-status-yellow)] dark:text-primary',
        success:
          'bg-[var(--color-status-green)] text-white shadow-sm hover:bg-[var(--color-status-green)]/90 hover:shadow-md focus-visible:ring-[var(--color-status-green)]/50 black:shadow-[0_2px_8px_rgba(74,222,128,0.3)] dark:shadow-[0_2px_8px_rgba(74,222,128,0.2)]',
        warning:
          'bg-[var(--color-status-yellow)] text-black shadow-sm hover:bg-[var(--color-status-yellow)]/90 hover:shadow-md focus-visible:ring-[var(--color-status-yellow)]/50 black:shadow-[0_2px_8px_rgba(251,191,36,0.3)] dark:shadow-[0_2px_8px_rgba(251,191,36,0.2)]',
        ton: 'bg-ton-main text-white shadow-sm hover:bg-ton-main/90 hover:shadow-md focus-visible:ring-ton-main/50 black:shadow-[0_2px_8px_rgba(0,152,234,0.3)] dark:shadow-[0_2px_8px_rgba(0,152,234,0.2)]',
      },
      size: {
        default: 'h-9 px-4 py-2 has-[>svg]:px-3',
        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5 text-xs',
        lg: 'h-11 rounded-lg px-6 has-[>svg]:px-5 text-base',
        xl: 'h-12 rounded-lg px-8 has-[>svg]:px-6 text-lg',
        icon: 'size-9',
        'icon-sm': 'size-8',
        'icon-lg': 'size-11',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<'button'> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : 'button';

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
}

export { Button, buttonVariants };
