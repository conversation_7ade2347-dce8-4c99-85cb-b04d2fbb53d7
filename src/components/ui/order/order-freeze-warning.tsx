import { AlertTriangle } from 'lucide-react';

import type {
  CollectionEntity,
  OrderEntity,
  UserType,
} from '@/marketplace-shared';
import {
  calculateFreezeEndDate,
  formatFreezeEndDate,
  shouldShowFreezeWarning,
} from '@/services/order-service';

interface OrderFreezeWarningProps {
  order: OrderEntity;
  userType: UserType;
  isFreezed: boolean;
  collection: CollectionEntity | null;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function OrderFreezeWarning({
  order,
  userType,
  isFreezed,
  collection,
  className = '',
  size = 'sm',
}: OrderFreezeWarningProps) {
  if (!shouldShowFreezeWarning(order, userType, isFreezed)) {
    return null;
  }

  const freezeEndDate = calculateFreezeEndDate(collection);

  const sizeClasses = {
    sm: {
      container: 'p-1.5 text-[10px]',
      icon: 'w-2.5 h-2.5',
      text: 'text-[9px]',
    },
    md: {
      container: 'p-3 text-sm',
      icon: 'w-4 h-4',
      text: 'text-xs',
    },
    lg: {
      container: 'p-4 text-base',
      icon: 'w-5 h-5',
      text: 'text-sm',
    },
  };

  const classes = sizeClasses[size];

  return (
    <div
      className={`bg-[var(--color-status-yellow-bg)] border border-[var(--color-status-yellow-border)] rounded ${classes.container} ${className}`}
    >
      <div className="flex items-center gap-1 mb-0.5">
        <AlertTriangle
          className={`${classes.icon} text-[var(--color-status-yellow)]`}
        />
        <span className="text-[var(--color-status-yellow)]">Freeze period</span>
      </div>
      {freezeEndDate && (
        <div className={`text-muted-foreground ${classes.text}`}>
          Ends: {formatFreezeEndDate(freezeEndDate)}
        </div>
      )}
    </div>
  );
}
