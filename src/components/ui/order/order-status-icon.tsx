import {
  AlertTriangle,
  CheckCircle,
  Clock,
  Gift,
  Plus,
  XCircle,
} from 'lucide-react';

import { OrderStatus } from '@/marketplace-shared';

interface OrderStatusIconProps {
  status: OrderStatus;
  className?: string;
}

const statusIconMap = {
  [OrderStatus.CREATED]: { Icon: Plus, colorClass: 'text-gray-200' },
  [OrderStatus.ACTIVE]: { Icon: Clock, colorClass: 'text-blue-200' },
  [OrderStatus.PAID]: { Icon: AlertTriangle, colorClass: 'text-yellow-200' },
  [OrderStatus.GIFT_SENT_TO_RELAYER]: {
    Icon: Gift,
    colorClass: 'text-purple-200',
  },
  [OrderStatus.FULFILLED]: { Icon: CheckCircle, colorClass: 'text-green-200' },
  [OrderStatus.CANCELLED]: { Icon: XCircle, colorClass: 'text-red-200' },
};

export function OrderStatusIcon({
  status,
  className = 'w-4 h-4',
}: OrderStatusIconProps) {
  const iconConfig = statusIconMap[status];

  if (!iconConfig) {
    return null;
  }

  const { Icon, colorClass } = iconConfig;
  return <Icon className={`${className} ${colorClass}`} />;
}
