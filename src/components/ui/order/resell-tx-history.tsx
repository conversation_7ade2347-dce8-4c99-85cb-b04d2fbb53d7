'use client';

import { Calendar, TrendingUp, User } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';

import {
  getResellHistoryByOrderId,
  type ResellTxHistoryWithUsers,
} from '@/api/resell-tx-history-api';
import { PriceLabel } from '@/components/shared/price-label';
import { TonLogo } from '@/components/TonLogo';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  firebaseTimestampToDate,
  type OrderEntity,
} from '@/marketplace-shared';
import { useRootContext } from '@/root-context';

import { resellTxHistoryMessages } from './intl/resell-tx-history.messages';

interface ResellTxHistoryProps {
  order: OrderEntity;
  onClose: () => void;
}

interface ResellTxHistoryItemProps {
  transaction: ResellTxHistoryWithUsers;
  isOriginalSeller: boolean;
  order: OrderEntity;
}

function ResellTxHistoryItem({
  transaction,
  isOriginalSeller,
  order,
}: ResellTxHistoryItemProps) {
  const { formatMessage: t } = useIntl();
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const calculateSellerEarnings = () => {
    if (!isOriginalSeller || !order.fees?.resell_purchase_fee_for_seller) {
      return 0;
    }
    const executionPrice = parseFloat(transaction.execution_price);
    const feeBPS = order.fees.resell_purchase_fee_for_seller;
    return (executionPrice * feeBPS) / 10000; // Convert BPS to decimal
  };

  const sellerEarnings = calculateSellerEarnings();

  return (
    <Card className="bg-card border-border/30">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium text-foreground">
              {t(resellTxHistoryMessages.resellTransaction)}
            </span>
          </div>
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3 text-muted-foreground" />
            <span className="text-xs text-muted-foreground">
              {formatDate(firebaseTimestampToDate(transaction.executed_at))}
            </span>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">
              {t(resellTxHistoryMessages.executionPrice)}
            </span>
            <div className="flex items-center gap-1">
              <TonLogo className="w-4 h-4" />
              <PriceLabel
                amount={parseFloat(transaction.execution_price)}
                className="text-foreground font-medium"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="flex items-center gap-1 mb-1">
                <User className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">
                  {t(resellTxHistoryMessages.reseller)}
                </span>
              </div>
              <div className="text-sm text-foreground">
                {transaction.reseller?.displayName ??
                  transaction.reseller?.email ??
                  `User ${transaction.reseller_id.slice(0, 8)}...`}
              </div>
              {transaction.reseller?.telegram_handle && (
                <div className="text-xs text-muted-foreground">
                  @{transaction.reseller.telegram_handle}
                </div>
              )}
            </div>

            <div>
              <div className="flex items-center gap-1 mb-1">
                <User className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">
                  {t(resellTxHistoryMessages.buyer)}
                </span>
              </div>
              <div className="text-sm text-foreground">
                {transaction.buyer?.displayName ??
                  transaction.buyer?.email ??
                  `User ${transaction.buyer_id.slice(0, 8)}...`}
              </div>
              {transaction.buyer?.telegram_handle && (
                <div className="text-xs text-muted-foreground">
                  @{transaction.buyer.telegram_handle}
                </div>
              )}
            </div>
          </div>

          {isOriginalSeller && sellerEarnings > 0 && (
            <div className="pt-2 border-t border-border/30">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  {t(resellTxHistoryMessages.yourEarnings)}
                </span>
                <div className="flex items-center gap-1">
                  <TonLogo className="w-4 h-4" />
                  <PriceLabel
                    amount={sellerEarnings}
                    className="text-[var(--color-status-green)] font-medium"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export function ResellTxHistory({ order, onClose }: ResellTxHistoryProps) {
  const { currentUser } = useRootContext();
  const { formatMessage: t } = useIntl();
  const [transactions, setTransactions] = useState<ResellTxHistoryWithUsers[]>(
    [],
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const isOriginalSeller = currentUser?.id === order.sellerId;

  useEffect(() => {
    const fetchResellHistory = async () => {
      if (!order.id) return;

      try {
        setLoading(true);
        setError(null);
        const response = await getResellHistoryByOrderId(order.id);

        if (response.success) {
          setTransactions(response.transactions);
        } else {
          setError(
            response.message ?? t(resellTxHistoryMessages.failedToFetchHistory),
          );
        }
      } catch (err) {
        console.error('Error fetching resell history:', err);
        setError(t(resellTxHistoryMessages.failedToFetchHistory));
      } finally {
        setLoading(false);
      }
    };

    fetchResellHistory();
  }, [order.id]);

  if (loading) {
    return (
      <Card className="bg-card border-border/30">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-foreground">
            <TrendingUp className="h-5 w-5" />
            {t(resellTxHistoryMessages.resellHistory)}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full mx-auto mb-2"></div>
            <p className="text-muted-foreground">
              {t(resellTxHistoryMessages.loadingResellHistory)}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="bg-card border-border/30">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-foreground">
            <TrendingUp className="h-5 w-5" />
            {t(resellTxHistoryMessages.resellHistory)}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button
            onClick={onClose}
            variant="outline"
            className="w-full mt-4 border-border text-foreground hover:bg-border/20"
          >
            {t(resellTxHistoryMessages.close)}
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (transactions.length === 0) {
    return (
      <Card className="bg-card border-border/30">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-foreground">
            <TrendingUp className="h-5 w-5" />
            {t(resellTxHistoryMessages.resellHistory)}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <TrendingUp className="h-8 w-8 text-muted-foreground mx-auto mb-2 opacity-50" />
            <p className="text-muted-foreground">
              {t(resellTxHistoryMessages.noResellTransactions)}
            </p>
          </div>
          <Button
            onClick={onClose}
            variant="outline"
            className="w-full mt-4 border-border text-foreground hover:bg-border/20"
          >
            {t(resellTxHistoryMessages.close)}
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-card border-border/30">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-foreground">
          <TrendingUp className="h-5 w-5" />
          {t(resellTxHistoryMessages.resellHistoryCount, {
            count: transactions.length,
          })}
        </CardTitle>
        {isOriginalSeller && (
          <p className="text-sm text-muted-foreground">
            {t(resellTxHistoryMessages.showingEarnings)}
          </p>
        )}
      </CardHeader>
      <CardContent className="space-y-3">
        {transactions.map((transaction) => (
          <ResellTxHistoryItem
            key={transaction.id}
            transaction={transaction}
            isOriginalSeller={isOriginalSeller}
            order={order}
          />
        ))}
        <Button
          onClick={onClose}
          variant="outline"
          className="w-full mt-4 border-border text-foreground hover:bg-border/20"
        >
          {t(resellTxHistoryMessages.close)}
        </Button>
      </CardContent>
    </Card>
  );
}
