'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useTgUiStyles } from '@/hooks/use-tg-ui-styles';
import { useThemeSwitcher } from '@/hooks/use-theme-switcher';

/**
 * Demo component to show theme-aware TG UI styles
 * This component demonstrates how the --tgui--button_color changes
 * to brighter glittery dark gold (#daa520) when the black theme is active
 * All colors are managed through CSS variables for automatic theme adaptation
 */
export function ThemeAwareTgDemo() {
  const { theme, switchTheme, getThemeLabel } = useThemeSwitcher();
  const tgUiStyles = useTgUiStyles();

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Theme-Aware TG UI Styles Demo</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <p className="text-sm text-muted-foreground mb-2">
            Current theme:{' '}
            <span className="font-semibold">{getThemeLabel(theme)}</span>
          </p>
          <div className="flex gap-2">
            <Button
              size="sm"
              variant={theme === 'default' ? 'default' : 'outline'}
              onClick={() => switchTheme('default')}
            >
              Light
            </Button>
            <Button
              size="sm"
              variant={theme === 'dark' ? 'default' : 'outline'}
              onClick={() => switchTheme('dark')}
            >
              Dark
            </Button>
            <Button
              size="sm"
              variant={theme === 'black' ? 'default' : 'outline'}
              onClick={() => switchTheme('black')}
            >
              Black
            </Button>
          </div>
        </div>

        <div>
          <p className="text-sm text-muted-foreground mb-2">
            TG UI Button Color:
          </p>
          <div
            className="w-full h-12 rounded-md border flex items-center justify-center text-sm font-medium"
            style={{
              backgroundColor: tgUiStyles['--tgui--button_color'],
              color: tgUiStyles['--tgui--button_text_color'],
            }}
          >
            {tgUiStyles['--tgui--button_color']}
          </div>
        </div>

        <div>
          <p className="text-sm text-muted-foreground mb-2">
            All TG UI Styles:
          </p>
          <div className="text-xs bg-muted p-3 rounded-md max-h-40 overflow-y-auto">
            <pre className="whitespace-pre-wrap">
              {JSON.stringify(tgUiStyles, null, 2)}
            </pre>
          </div>
        </div>

        <div className="text-xs text-muted-foreground">
          <p>
            <strong>Note:</strong> When the black theme is active, the{' '}
            <code>--tgui--button_color</code> changes to brighter glittery dark
            gold (#daa520). All colors are now managed through CSS variables
            that automatically adapt to the current theme.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
