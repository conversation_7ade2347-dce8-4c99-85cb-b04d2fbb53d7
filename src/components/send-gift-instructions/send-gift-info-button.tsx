'use client';

import { Button } from '@telegram-apps/telegram-ui';
import { Plus } from 'lucide-react';
import { useState } from 'react';

import { cn } from '@/lib/utils';

import { SendGiftInstructionsDrawer } from './send-gift-instructions-drawer';

interface SendGiftInfoButtonProps {
  className?: string;
}

export function SendGiftInfoButton({ className }: SendGiftInfoButtonProps) {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  return (
    <>
      <Button
        size="s"
        onClick={() => setIsDrawerOpen(true)}
        className={cn(
          'bg-primary !border-primary !border-1 text-primary-foreground hover:bg-primary/90 [&>h6]:flex [&>h6]:items-center [&>h6]:gap-2 rounded-full w-14 h-14 overflow-hidden',
          className,
        )}
      >
        <Plus className="w-6 h-6" />
      </Button>

      <SendGiftInstructionsDrawer
        open={isDrawerOpen}
        onOpenChange={setIsDrawerOpen}
      />
    </>
  );
}
