'use client';

import { ExternalLink } from 'lucide-react';
import { FormattedMessage, useIntl } from 'react-intl';

import { Button } from '@/components/ui/button';
import { BaseDrawer } from '@/components/ui/drawer/base-drawer';
import { DrawerHeader } from '@/components/ui/drawer/drawer-header';
import { PREM_RELAYER_URL } from '@/core.constants';

import { sendGiftInstructionsMessages } from './intl/send-gift-instructions.messages';

interface SendGiftInstructionsDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function SendGiftInstructionsDrawer({
  open,
  onOpenChange,
}: SendGiftInstructionsDrawerProps) {
  const { formatMessage: t } = useIntl();

  const relayerLink = (chunks: React.ReactNode) => (
    <a
      href={PREM_RELAYER_URL}
      target="_blank"
      rel="noopener noreferrer"
      className="text-[var(--color-status-blue)] hover:text-[var(--color-status-blue)]/80 underline"
    >
      {chunks}
    </a>
  );

  const instructions = [
    <FormattedMessage
      {...sendGiftInstructionsMessages.step1}
      key="step1"
      values={{ relayerLink }}
    />,
    t(sendGiftInstructionsMessages.step2),
    <FormattedMessage
      {...sendGiftInstructionsMessages.step3}
      key="step4"
      values={{ relayerLink }}
    />,
    t(sendGiftInstructionsMessages.step4),
    t(sendGiftInstructionsMessages.step5),
    t(sendGiftInstructionsMessages.step6),
    t(sendGiftInstructionsMessages.step7),
  ];

  const handleOpenRelayer = () => {
    window.open(PREM_RELAYER_URL, '_blank');
  };

  return (
    <BaseDrawer open={open} onOpenChange={onOpenChange} zIndex={50}>
      <DrawerHeader title={t(sendGiftInstructionsMessages.title)} />

      <div className="space-y-4">
        <div className="bg-card border border-border rounded-lg p-4">
          <ol className="space-y-2">
            {instructions.map((instruction, index) => (
              <li key={index} className="flex items-start gap-3">
                <span className="flex-shrink-0 w-6 h-6 bg-[var(--color-status-blue-bg)] text-[var(--color-status-blue)] rounded-full flex items-center justify-center text-xs font-semibold">
                  {index + 1}
                </span>
                <span className="text-sm text-muted-foreground leading-relaxed">
                  {instruction}
                </span>
              </li>
            ))}
          </ol>
        </div>

        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="flex-1 border-border text-muted-foreground hover:bg-card"
          >
            {t(sendGiftInstructionsMessages.close)}
          </Button>
          <Button
            onClick={handleOpenRelayer}
            className="flex-1 bg-[var(--color-status-blue)] hover:bg-[var(--color-status-blue)]/90 text-white"
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            {t(sendGiftInstructionsMessages.openRelayer)}
          </Button>
        </div>
      </div>
    </BaseDrawer>
  );
}
