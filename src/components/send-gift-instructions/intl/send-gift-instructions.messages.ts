import { defineMessages } from 'react-intl';

export const sendGiftInstructionsMessages = defineMessages({
  title: {
    id: 'sendGiftInstructions.title',
    defaultMessage: 'Send Gift Instructions',
  },

  step1: {
    id: 'sendGiftInstructions.step1',
    defaultMessage:
      'Go to the bot via the link - <relayerLink>@premrelayer</relayerLink>',
  },
  step2: {
    id: 'sendGiftInstructions.step2',
    defaultMessage: 'Make sure the nickname exactly matches the one specified',
  },
  step3: {
    id: 'sendGiftInstructions.step3',
    defaultMessage:
      'Send the gift to the bot <relayerLink>@premrelayer</relayerLink>',
  },
  step4: {
    id: 'sendGiftInstructions.step4',
    defaultMessage: 'Click on profile - 3 dots - "Send gift"',
  },
  step5: {
    id: 'sendGiftInstructions.step5',
    defaultMessage:
      'Your gifts are completely safe and will be linked to your account in PREM',
  },
  step6: {
    id: 'sendGiftInstructions.step6',
    defaultMessage: 'The gift will appear within a minute',
  },
  step7: {
    id: 'sendGiftInstructions.step7',
    defaultMessage: 'If the gift does not appear - refresh the page',
  },
  close: {
    id: 'sendGiftInstructions.close',
    defaultMessage: 'Close',
  },

  openRelayer: {
    id: 'sendGiftInstructions.openRelayer',
    defaultMessage: 'Open Relayer',
  },
});
