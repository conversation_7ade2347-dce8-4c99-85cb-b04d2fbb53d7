'use client';

import { useEffect, useState } from 'react';

import { BASE_TG_UI_STYLES, getTgUiStyles } from '@/core.constants';
import { useThemeSwitcher } from '@/hooks/use-theme-switcher';

/**
 * Hook to get theme-aware TG UI styles
 * Returns styles that adapt to the current theme using CSS variables
 * The button color automatically becomes brighter gold (#daa520) in black theme
 */
export function useTgUiStyles(): Record<string, string> {
  const { theme } = useThemeSwitcher();
  const [styles, setStyles] =
    useState<Record<string, string>>(BASE_TG_UI_STYLES);

  useEffect(() => {
    // Update styles when theme changes
    // The CSS variables will automatically reflect the current theme
    setStyles(getTgUiStyles());
  }, [theme]);

  return styles;
}
