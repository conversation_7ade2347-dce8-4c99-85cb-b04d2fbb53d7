'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { Role } from '@/marketplace-shared';
import { useRootContext } from '@/root-context';

export const useAdminAuth = () => {
  const { currentUser, currentUserLoading } = useRootContext();
  const router = useRouter();

  useEffect(() => {
    if (
      (!currentUser || currentUser.role !== Role.ADMIN) &&
      !currentUserLoading
    ) {
      router.push('/');
    }
  }, [currentUser, currentUserLoading, router]);

  const isAuthorized = currentUser && currentUser.role === Role.ADMIN;

  return {
    currentUser,
    isAuthorized,
    isLoading: !currentUser,
  };
};
