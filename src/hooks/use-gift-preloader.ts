import { useEffect } from 'react';

import type { OrderGift } from '@/marketplace-shared';
import { preloadGiftAssets } from '@/services/gift-image-service';

interface UseGiftPreloaderOptions {
  gifts: (OrderGift | null | undefined)[];
  enabled?: boolean;
  delay?: number; // Delay before starting preload (ms)
}

/**
 * Hook to preload gift assets for better performance
 * Use this in components that display lists of orders with gifts
 */
export function useGiftPreloader({
  gifts,
  enabled = true,
  delay = 100,
}: UseGiftPreloaderOptions) {
  useEffect(() => {
    if (!enabled || !gifts.length) return;

    const timer = setTimeout(() => {
      gifts.forEach((gift) => {
        if (gift) {
          // Preload PNG versions for faster loading
          preloadGiftAssets(gift, true);
        }
      });
    }, delay);

    return () => clearTimeout(timer);
  }, [gifts, enabled, delay]);
}

/**
 * Preload a single gift immediately
 */
export function preloadSingleGift(gift: OrderGift | null | undefined) {
  if (gift) {
    preloadGiftAssets(gift, true);
  }
}
