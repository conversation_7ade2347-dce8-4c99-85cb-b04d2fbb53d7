import { Context } from "telegraf";
import { BOT_TOKEN } from "../app.constants";
import { loadEnvironment } from "../config/env-loader";
import { T } from "../i18n";
import { botMessages } from "../intl/messages";
import { OrderGift } from "../marketplace-shared";
import { BusinessConnectionHelpersLogger } from "./business-connection-helpers.logger";

loadEnvironment();

const isGiftComplete = (gift: any): boolean => {
  // Check if all required fields exist
  const hasModel =
    gift.model?.name && typeof gift.model.rarity_per_mille === "number";
  const hasSymbol =
    gift.symbol?.name && typeof gift.symbol.rarity_per_mille === "number";
  const hasBackdrop =
    gift.backdrop?.name &&
    gift.backdrop?.colors &&
    typeof gift.backdrop.rarity_per_mille === "number";

  return hasModel && hasSymbol && hasBackdrop;
};

export const getGiftToTransfer = (ctx: Context): OrderGift | undefined => {
  const businessMessage = (ctx.update as any).business_message;
  const uniqueGift = businessMessage?.unique_gift;

  const innerGift = uniqueGift?.gift;

  if (!innerGift) {
    return undefined;
  }

  // Skip gifts that don't have model, symbol, or backdrop fields
  if (!isGiftComplete(innerGift)) {
    return undefined;
  }

  return {
    base_name: uniqueGift.gift.base_name,
    owned_gift_id: uniqueGift.owned_gift_id,
    backdrop: uniqueGift.gift.backdrop,
    model: {
      name: uniqueGift.gift.model.name,
      rarity_per_mille: uniqueGift.gift.model.rarity_per_mille,
    },
    symbol: {
      name: uniqueGift.gift.symbol.name,
      rarity_per_mille: uniqueGift.gift.symbol.rarity_per_mille,
    },
  };
};

export const getGiftCollectionId = (ctx: Context): string | undefined => {
  const businessMessage = (ctx.update as any).business_message;
  
  const base_name = businessMessage?.unique_gift?.gift?.base_name;

  

  return collectionId;
};

export const getBusinessConnectionId = (ctx: Context): string | undefined => {
  const businessMessage = (ctx.update as any).business_message;
  return businessMessage?.business_connection_id;
};

export const getUniqueGift = (ctx: Context): any => {
  const businessMessage = (ctx.update as any).business_message;
  return businessMessage?.unique_gift;
};

export const transferGift = async (
  ctx: Context,
  businessConnectionId: string,
  chatId: number,
  owned_gift_id: string
): Promise<void> => {
  try {
    const sendGiftResponse = await fetch(
      `https://api.telegram.org/bot${BOT_TOKEN}/transferGift`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          business_connection_id: businessConnectionId,
          new_owner_chat_id: chatId,
          star_count: 25,
          owned_gift_id,
        }),
      }
    );

    const sendGiftResult = (await sendGiftResponse.json()) as {
      ok: boolean;
      description?: string;
      error_code?: number;
    };

    if (sendGiftResult.ok) {
      BusinessConnectionHelpersLogger.logGiftTransferSuccess({
        chatId: String(chatId),
        owned_gift_id,
      });
      await ctx.telegram.sendMessage(
        chatId,
        T(ctx, botMessages.giftTransferredSuccess.id)
      );
    } else {
      BusinessConnectionHelpersLogger.logGiftTransferFailed({
        sendGiftResult,
        chatId: String(chatId),
        owned_gift_id,
      });
      await ctx.telegram.sendMessage(
        chatId,
        T(ctx, botMessages.giftTransferGenericError.id)
      );
    }
  } catch (error) {
    BusinessConnectionHelpersLogger.logGiftTransferError({
      error,
      chatId: String(chatId),
      owned_gift_id,
    });
    await ctx.telegram.sendMessage(
      chatId,
      T(ctx, botMessages.giftTransferGenericError.id)
    );
  }
};
