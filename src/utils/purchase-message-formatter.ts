import type { IntlShape } from 'react-intl';

import type { PurchaseData } from '@/api/orders-api';
import { UserType } from '@/marketplace-shared';

import { purchaseMessages } from '../intl/purchase.messages';

export interface PurchaseMessageParams {
  purchaseData: PurchaseData;
  t: IntlShape['formatMessage'];
}

export function formatPurchaseSuccessMessage({
  purchaseData,
  t,
}: PurchaseMessageParams): string {
  const {
    userType,
    shouldLockFunds,
    shouldSkipToPaidStatus,
    lockedAmount,
    orderAmount,
    lockPercentage,
    feeApplied,
    netAmountToSeller,
  } = purchaseData;

  // Get the appropriate action message
  const actionMessage = getActionMessage({
    userType: userType as UserType,
    shouldSkipToPaidStatus,
    t,
  });

  // Construct the complete message based on scenario
  if (userType === UserType.BUYER) {
    if (shouldLockFunds) {
      // Buyer with funds locked
      if (feeApplied > 0) {
        return t(purchaseMessages.buyerPurchaseWithLockAndFee, {
          lockedAmount: lockedAmount.toFixed(2),
          lockPercentage: lockPercentage.toString(),
          orderPrice: orderAmount.toFixed(2),
          totalFee: feeApplied.toFixed(2),
          actionMessage,
        });
      } else {
        return t(purchaseMessages.buyerPurchaseWithLockNoFee, {
          lockedAmount: lockedAmount.toFixed(2),
          lockPercentage: lockPercentage.toString(),
          orderPrice: orderAmount.toFixed(2),
          actionMessage,
        });
      }
    } else {
      // Buyer with payment completed (gift scenario)
      if (feeApplied > 0) {
        return t(purchaseMessages.buyerPurchasePaymentCompletedWithFee, {
          netAmountToSeller: netAmountToSeller.toFixed(2),
          totalFee: feeApplied.toFixed(2),
          actionMessage,
        });
      } else {
        return t(purchaseMessages.buyerPurchasePaymentCompletedNoFee, {
          netAmountToSeller: netAmountToSeller.toFixed(2),
          actionMessage,
        });
      }
    }
  } else {
    // Seller scenario (always with funds locked)
    return t(purchaseMessages.sellerPurchaseWithLock, {
      lockedAmount: lockedAmount.toFixed(2),
      lockPercentage: lockPercentage.toString(),
      orderPrice: orderAmount.toFixed(2),
      actionMessage,
    });
  }
}

function getActionMessage({
  userType,
  shouldSkipToPaidStatus,
  t,
}: {
  userType: UserType;
  shouldSkipToPaidStatus: boolean;
  t: IntlShape['formatMessage'];
}): string {
  if (userType === UserType.BUYER) {
    if (shouldSkipToPaidStatus) {
      return t(purchaseMessages.buyerGiftReadyToClaim);
    } else {
      return t(purchaseMessages.buyerWaitingForSeller);
    }
  } else {
    return t(purchaseMessages.sellerCanSendGift);
  }
}
