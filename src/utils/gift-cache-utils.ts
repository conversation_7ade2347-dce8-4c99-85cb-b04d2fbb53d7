import type { OrderGift } from '@/marketplace-shared';
import { globalCache } from '@/utils/cache-utils';

const GIFT_CACHE_CONFIG = { duration: 60 * 60 * 1000 }; // 1 hour

interface GiftAssetCache {
  modelUrl: string;
  patternUrl: string;
  preloaded: boolean;
  timestamp: number;
}

/**
 * Cache gift asset URLs for faster access
 */
export const cacheGiftAssets = (
  gift: OrderGift,
  isImage: boolean = false,
): GiftAssetCache => {
  const format = isImage ? 'png' : 'tgs';
  const modelUrl = `https://cdn.changes.tg/gifts/models/${gift.base_name}/${format === 'png' ? 'png/' : ''}${gift.model.name}.${format}`;
  const patternUrl = `https://cdn.changes.tg/gifts/patterns/${gift.base_name}/${format === 'png' ? 'png/' : ''}${gift.symbol.name}.${format}`;

  const cacheData: GiftAssetCache = {
    modelUrl,
    patternUrl,
    preloaded: false,
    timestamp: Date.now(),
  };

  const cacheKey = `gift:${gift.base_name}:${gift.model.name}:${gift.symbol.name}:${format}`;
  globalCache.set(cacheKey, cacheData, GIFT_CACHE_CONFIG);

  return cacheData;
};

/**
 * Get cached gift asset URLs
 */
export const getCachedGiftAssets = (
  gift: OrderGift,
  isImage: boolean = false,
): GiftAssetCache | null => {
  const format = isImage ? 'png' : 'tgs';
  const cacheKey = `gift:${gift.base_name}:${gift.model.name}:${gift.symbol.name}:${format}`;
  return globalCache.get<GiftAssetCache>(cacheKey);
};

/**
 * Mark gift assets as preloaded
 */
export const markGiftAssetsPreloaded = (
  gift: OrderGift,
  isImage: boolean = false,
): void => {
  const cached = getCachedGiftAssets(gift, isImage);
  if (cached) {
    cached.preloaded = true;
    cached.timestamp = Date.now();
    const format = isImage ? 'png' : 'tgs';
    const cacheKey = `gift:${gift.base_name}:${gift.model.name}:${gift.symbol.name}:${format}`;
    globalCache.set(cacheKey, cached, GIFT_CACHE_CONFIG);
  }
};

/**
 * Check if gift assets are preloaded
 */
export const areGiftAssetsPreloaded = (
  gift: OrderGift,
  isImage: boolean = false,
): boolean => {
  const cached = getCachedGiftAssets(gift, isImage);
  return cached?.preloaded || false;
};

/**
 * Batch preload multiple gifts
 */
export const batchPreloadGifts = async (
  gifts: OrderGift[],
  isImage: boolean = true,
): Promise<void> => {
  const preloadPromises = gifts.map((gift) => {
    const cached = cacheGiftAssets(gift, isImage);

    // Create image preload promises
    const modelPromise = new Promise<void>((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve();
      img.onerror = () => reject();
      img.src = cached.modelUrl;
    });

    const patternPromise = new Promise<void>((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve();
      img.onerror = () => reject();
      img.src = cached.patternUrl;
    });

    return Promise.all([modelPromise, patternPromise])
      .then(() => markGiftAssetsPreloaded(gift, isImage))
      .catch(() => {
        // Silently fail individual gifts
      });
  });

  await Promise.allSettled(preloadPromises);
};

/**
 * Clear expired gift cache entries
 */
export const clearExpiredGiftCache = (): void => {
  globalCache.invalidatePattern('gift:');
};
