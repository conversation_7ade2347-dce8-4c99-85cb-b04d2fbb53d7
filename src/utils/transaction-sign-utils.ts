/**
 * Standardized transaction amount sign rules (from platform balance perspective):
 * - DEPOSIT: Always positive (+) - money added to user's platform balance
 * - WITHDRAW: Always negative (-) - money removed from user's platform balance
 * - BUY_LOCK_COLLATERAL: Always negative (-) - buyer's collateral locked when creating buy order
 * - UNLOCK_COLLATERAL: Always positive (+) - collateral returned to user
 * - SELL_LOCK_COLLATERAL: Always negative (-) - seller's collateral locked when creating sell order
 * - REFERRAL_FEE: Always positive (+) - earnings received by referrer
 * - CANCELATION_FEE: Context-dependent sign:
 *   - Positive (+) when receiving compensation for counterparty cancellation
 *   - Negative (-) when paying penalty for own cancellation
 * - REFUND: Always positive (+) - money returned to user
 * - SELL_FULFILLMENT: Always positive (+) - seller receiving buyer's payment upon order completion
 * - RESELL_FEE_EARNINGS: Always positive (+) - earnings from reselling an order
 */

import type { TxType } from '@/marketplace-shared';

export function applyTransactionSign(
  amount: number,
  txType: TxType,
  isReceivingCompensation?: boolean,
): number {
  if (amount < 0) {
    throw new Error('Amount must be positive when applying transaction sign');
  }

  switch (txType) {
    case 'deposit':
    case 'unlock_collateral':
    case 'referral_fee':
    case 'refund':
    case 'sell_fulfillment':
    case 'resell_fee_earnings':
      return amount; // Money added to platform balance

    case 'withdraw':
    case 'buy_lock_collateral':
    case 'sell_lock_collateral':
      return -amount; // Money removed from platform balance

    case 'proposal_collateral_lock':
      return -amount; // Money locked for proposal

    case 'proposal_collateral_unlock':
    case 'proposal_collateral_refund':
      return amount; // Money returned from proposal

    case 'cancelation_fee':
      if (isReceivingCompensation === undefined) {
        throw new Error(
          'isReceivingCompensation must be specified for CANCELATION_FEE transactions',
        );
      }
      return isReceivingCompensation ? amount : -amount;

    default:
      throw new Error(`Unknown transaction type: ${txType}`);
  }
}

export function getTransactionAmountColor(amount: number): string {
  return amount >= 0
    ? 'text-[var(--color-transaction-positive)]'
    : 'text-[var(--color-transaction-negative)]';
}

export function formatTransactionAmount(amount: number): string {
  const sign = amount >= 0 ? '+' : '-';
  const absoluteAmount = Math.abs(amount);
  return `${sign}${absoluteAmount.toFixed(2)} TON`;
}
