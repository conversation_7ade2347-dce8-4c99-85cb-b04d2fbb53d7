import type { IntlShape } from 'react-intl';
import { toast } from 'sonner';

import {
  makePurchaseAsBuyer,
  makePurchaseAsSeller,
  makeSecondaryMarketPurchase,
} from '@/api/orders-api';
import { formatServerError } from '@/api/server-error-handler';
import { UserType } from '@/marketplace-shared';
import { formatPurchaseSuccessMessage } from '@/utils/purchase-message-formatter';

export interface OrderActionResult {
  success: boolean;
  message?: string;
}

export async function executeMarketplaceOrderAction(
  orderId: string,
  userType: UserType,
  t: IntlShape['formatMessage'],
): Promise<OrderActionResult> {
  try {
    let result;
    if (userType === UserType.BUYER) {
      result = await makePurchaseAsSeller(orderId);
    } else {
      result = await makePurchaseAsBuyer(orderId);
    }

    // Use new structured message formatting
    const message = result.purchaseData
      ? formatPurchaseSuccessMessage({
          purchaseData: result.purchaseData,
          t,
        })
      : 'Action completed successfully!';

    toast.success(message);
    return { success: true, message };
  } catch (error: unknown) {
    console.error('Marketplace action failed:', error);
    const errorMessage = formatServerError(error, t);
    toast.error(errorMessage);
    return { success: false, message: errorMessage };
  }
}

export async function executeSecondaryMarketPurchase(
  orderId: string,
  formatMessage: IntlShape['formatMessage'],
): Promise<OrderActionResult> {
  try {
    const result = await makeSecondaryMarketPurchase(orderId);
    // Secondary market purchases still use the legacy message format
    const message =
      result.message ?? 'Secondary market purchase completed successfully!';
    toast.success(message);
    return { success: true, message };
  } catch (error: unknown) {
    console.error('Secondary market purchase failed:', error);
    const errorMessage = formatServerError(error, formatMessage);
    toast.error(errorMessage);
    return { success: false, message: errorMessage };
  }
}
