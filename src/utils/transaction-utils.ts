import { firebaseTimestampToDate } from '@/marketplace-shared';

import {
  formatTransactionAmount,
  getTransactionAmountColor,
} from './transaction-sign-utils';

export const formatAmount = (amount: number): string => {
  return formatTransactionAmount(amount);
};

export const getAmountColor = (amount: number): string => {
  return getTransactionAmountColor(amount);
};

export const formatTransactionDate = (date: Date | undefined): string => {
  if (!date) return '';
  return firebaseTimestampToDate(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};
