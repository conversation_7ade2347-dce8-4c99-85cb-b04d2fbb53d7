/* Default Theme Colors */
:root {
  /* Tailwind Color Utilities */
  --color-red-600: #dc2626;
  --color-green-600: #16a34a;
  --color-blue-500: #3b82f6;
  --color-yellow-500: #eab308;
  --color-purple-500: #a855f7;
  --color-pink-500: #ec4899;
  --color-orange-500: #f97316;
  --color-red-400: #f87171;
  --color-green-400: #4ade80;
  --color-blue-400: #60a5fa;
  --color-yellow-400: #facc15;
  --color-purple-400: #c084fc;
  --color-orange-400: #fb923c;
  /* Base Colors */
  --color-white: #ffffff;
  --color-black: #000000;
  --color-transparent: transparent;

  /* Gray Scale */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* TON Brand Colors */
  --color-ton-main: #0098ea;
  --color-ton-gray: #f7f9fb;
  --color-ton-black: #1e2337;

  /* Telegram Colors */
  --color-telegram-blue: #0088cc;
  --color-telegram-light-blue: #6ab2f2;
  --color-telegram-dark-bg: #17212b;
  --color-telegram-secondary-bg: #232e3c;
  --color-telegram-text: #f5f5f5;
  --color-telegram-hint: #708499;

  /* Status Colors */
  --color-success: #34c759;
  --color-success-light: #4ade80;
  --color-warning: #ff9500;
  --color-warning-light: #fbbf24;
  --color-error: #ff3b30;
  --color-error-light: #f87171;
  --color-info: #007aff;
  --color-info-light: #60a5fa;

  /* Semantic Colors - Light Theme */
  --color-primary: #007aff;
  --color-primary-foreground: #ffffff;
  --color-secondary: #f2f2f7;
  --color-secondary-foreground: #000000;
  --color-accent: #007aff;
  --color-accent-foreground: #ffffff;
  --color-destructive: #ff3b30;
  --color-destructive-foreground: #ffffff;
  --color-muted: #f2f2f7;
  --color-muted-foreground: #8e8e93;

  /* Background Colors */
  --color-background: #ffffff;
  --color-foreground: #000000;
  --color-card: #ffffff;
  --color-card-foreground: #000000;
  --color-popover: #ffffff;
  --color-popover-foreground: #000000;

  /* Border and Input Colors */
  --color-border: #c6c6c8;
  --color-input: #f2f2f7;
  --color-ring: #007aff;

  /* Chart Colors */
  --color-chart-1: #007aff;
  --color-chart-2: #34c759;
  --color-chart-3: #ff9500;
  --color-chart-4: #ff3b30;
  --color-chart-5: #af52de;

  /* Sidebar Colors */
  --color-sidebar: #f2f2f7;
  --color-sidebar-foreground: #000000;
  --color-sidebar-primary: #007aff;
  --color-sidebar-primary-foreground: #ffffff;
  --color-sidebar-accent: #f2f2f7;
  --color-sidebar-accent-foreground: #000000;
  --color-sidebar-border: #c6c6c8;
  --color-sidebar-ring: #007aff;

  /* Telegram Theme Colors */
  --color-tg-bg: #ffffff;
  --color-tg-text: #000000;
  --color-tg-hint: #8e8e93;
  --color-tg-link: #007aff;
  --color-tg-button: #007aff;
  --color-tg-button-text: #ffffff;
  --color-tg-secondary-bg: #f2f2f7;
  --color-tg-header-bg: #ffffff;
  --color-tg-bottom-bar-bg: #ffffff;
  --color-tg-accent-text: #007aff;
  --color-tg-section-bg: #ffffff;
  --color-tg-section-header-text: #007aff;
  --color-tg-section-separator: #c6c6c8;
  --color-tg-subtitle-text: #8e8e93;
  --color-tg-destructive-text: #ff3b30;

  /* Status Badge Colors */
  --color-status-yellow: #fbbf24;
  --color-status-yellow-bg: rgba(251, 191, 36, 0.1);
  --color-status-yellow-border: rgba(251, 191, 36, 0.2);
  --color-status-blue: #60a5fa;
  --color-status-blue-bg: rgba(96, 165, 250, 0.1);
  --color-status-blue-border: rgba(96, 165, 250, 0.2);
  --color-status-green: #4ade80;
  --color-status-green-bg: rgba(74, 222, 128, 0.1);
  --color-status-green-border: rgba(74, 222, 128, 0.2);
  --color-status-purple: #a855f7;
  --color-status-purple-bg: rgba(168, 85, 247, 0.1);
  --color-status-purple-border: rgba(168, 85, 247, 0.2);
  --color-status-pink: #ec4899;
  --color-status-pink-bg: rgba(236, 72, 153, 0.1);
  --color-status-orange: #f97316;
  --color-status-orange-bg: rgba(249, 115, 22, 0.1);
  --color-status-orange-border: rgba(249, 115, 22, 0.2);
  --color-status-red: #f87171;
  --color-status-red-bg: rgba(248, 113, 113, 0.1);
  --color-status-red-border: rgba(248, 113, 113, 0.2);

  /* Transaction Colors */
  --color-transaction-positive: #16a34a;
  --color-transaction-negative: #dc2626;

  /* Glass Effect Colors */
  --color-glass-light-from: rgba(255, 255, 255, 0.1);
  --color-glass-light-via: rgba(255, 255, 255, 0);
  --color-glass-light-to: rgba(255, 255, 255, 0.05);
  --color-glass-medium-from: rgba(255, 255, 255, 0.15);
  --color-glass-medium-via: rgba(255, 255, 255, 0.03);
  --color-glass-medium-to: rgba(255, 255, 255, 0.08);
  --color-glass-strong-from: rgba(255, 255, 255, 0.2);
  --color-glass-strong-via: rgba(255, 255, 255, 0.05);
  --color-glass-strong-to: rgba(255, 255, 255, 0.12);

  /* Shadow Colors */
  --color-shadow-light: rgba(0, 0, 0, 0.2);
  --color-shadow-medium: rgba(0, 0, 0, 0.3);
  --color-shadow-strong: rgba(0, 0, 0, 0.4);
}

/* Dark Theme Colors */
.dark {
  /* Semantic Colors - Dark Theme */
  --color-primary: #6ab2f2;
  --color-primary-foreground: #ffffff;
  --color-secondary: #232e3c;
  --color-secondary-foreground: #f5f5f5;
  --color-accent: #6ab2f2;
  --color-accent-foreground: #ffffff;
  --color-destructive: #ec3942;
  --color-destructive-foreground: #ffffff;
  --color-muted: #232e3c;
  --color-muted-foreground: #708499;

  /* Background Colors */
  --color-background: #17212b;
  --color-foreground: #f5f5f5;
  --color-card: #232e3c;
  --color-card-foreground: #f5f5f5;
  --color-popover: #232e3c;
  --color-popover-foreground: #f5f5f5;

  /* Border and Input Colors */
  --color-border: #3a4a5c;
  --color-input: #2a3441;
  --color-ring: #6ab2f2;

  /* Chart Colors */
  --color-chart-1: #6ab2f2;
  --color-chart-2: #5288c1;
  --color-chart-3: #708499;
  --color-chart-4: #ec3942;
  --color-chart-5: #6ab3f3;

  /* Sidebar Colors */
  --color-sidebar: #232e3c;
  --color-sidebar-foreground: #f5f5f5;
  --color-sidebar-primary: #6ab2f2;
  --color-sidebar-primary-foreground: #ffffff;
  --color-sidebar-accent: #232e3c;
  --color-sidebar-accent-foreground: #f5f5f5;
  --color-sidebar-border: #3a4a5c;
  --color-sidebar-ring: #6ab2f2;

  /* Telegram Theme Colors */
  --color-tg-bg: #17212b;
  --color-tg-text: #f5f5f5;
  --color-tg-hint: #708499;
  --color-tg-link: #6ab3f3;
  --color-tg-button: #5288c1;
  --color-tg-button-text: #ffffff;
  --color-tg-secondary-bg: #232e3c;
  --color-tg-header-bg: #17212b;
  --color-tg-bottom-bar-bg: #17212b;
  --color-tg-accent-text: #6ab2f2;
  --color-tg-section-bg: #17212b;
  --color-tg-section-header-text: #6ab3f3;
  --color-tg-section-separator: #3a4a5c;
  --color-tg-subtitle-text: #708499;
  --color-tg-destructive-text: #ec3942;
}

/* Black Theme Colors */
.black {
  /* Semantic Colors - Black Theme */
  --color-primary: #ffffff;
  --color-primary-foreground: #000000;
  --color-secondary: #1a1a1a;
  --color-secondary-foreground: #ffffff;
  --color-accent: #ffffff;
  --color-accent-foreground: #000000;
  --color-destructive: #ff4444;
  --color-destructive-foreground: #ffffff;
  --color-muted: #1a1a1a;
  --color-muted-foreground: #888888;

  /* Background Colors */
  --color-background: #000000;
  --color-foreground: #ffffff;
  --color-card: #1a1a1a;
  --color-card-foreground: #ffffff;
  --color-popover: #1a1a1a;
  --color-popover-foreground: #ffffff;

  /* Border and Input Colors */
  --color-border: #333333;
  --color-input: #1a1a1a;
  --color-ring: #ffffff;

  /* Chart Colors */
  --color-chart-1: #ffffff;
  --color-chart-2: #00ff00;
  --color-chart-3: #ffaa00;
  --color-chart-4: #ff4444;
  --color-chart-5: #aa44ff;

  /* Sidebar Colors */
  --color-sidebar: #1a1a1a;
  --color-sidebar-foreground: #ffffff;
  --color-sidebar-primary: #ffffff;
  --color-sidebar-primary-foreground: #000000;
  --color-sidebar-accent: #1a1a1a;
  --color-sidebar-accent-foreground: #ffffff;
  --color-sidebar-border: #333333;
  --color-sidebar-ring: #ffffff;

  /* Telegram Theme Colors */
  --color-tg-bg: #000000;
  --color-tg-text: #ffffff;
  --color-tg-hint: #888888;
  --color-tg-link: #ffffff;
  --color-tg-button: #daa520; /* Brighter glittery dark gold for black theme */
  --color-tg-button-text: #000000;
  --color-tg-secondary-bg: #1a1a1a;
  --color-tg-header-bg: #000000;
  --color-tg-bottom-bar-bg: #000000;
  --color-tg-accent-text: #ffffff;
  --color-tg-section-bg: #000000;
  --color-tg-section-header-text: #ffffff;
  --color-tg-section-separator: #333333;
  --color-tg-subtitle-text: #888888;
  --color-tg-destructive-text: #ff4444;
}
