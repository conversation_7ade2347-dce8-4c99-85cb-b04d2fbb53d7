import { TxType } from "../marketplace-shared";

/**
 * Standardized transaction amount sign rules (from platform balance perspective):
 * - DEPOSIT: Always positive (+) - money added to user's platform balance
 * - WITHDRAW: Always negative (-) - money removed from user's platform balance
 * - BUY_LOCK_COLLATERAL: Always negative (-) - buyer's collateral locked when creating buy order
 * - UNLOCK_COLLATERAL: Always positive (+) - collateral returned to user
 * - SELL_LOCK_COLLATERAL: Always negative (-) - seller's collateral locked when creating sell order
 * - REFERRAL_FEE: Always positive (+) - earnings received by referrer
 * - CANCELATION_FEE: Context-dependent sign:
 *   - Positive (+) when receiving compensation for counterparty cancellation
 *   - Negative (-) when paying penalty for own cancellation
 * - REFUND: Always positive (+) - money returned to user
 * - SELL_FULFILLMENT: Always positive (+) - seller receiving buyer's payment upon order completion
 * - RESELL_FEE_EARNINGS: Always positive (+) - earnings from reselling an order
 */

export function applyTransactionSign(
  amount: number,
  txType: TxType,
  isReceivingCompensation?: boolean
): number {
  if (amount < 0) {
    throw new Error("Amount must be positive when applying transaction sign");
  }

  switch (txType) {
    case TxType.DEPOSIT:
    case TxType.UNLOCK_COLLATERAL:
    case TxType.REFERRAL_FEE:
    case TxType.REFUND:
    case TxType.SELL_FULFILLMENT:
    case TxType.RESELL_FEE_EARNINGS:
      return amount; // Money added to platform balance

    case TxType.WITHDRAW:
    case TxType.BUY_LOCK_COLLATERAL:
    case TxType.SELL_LOCK_COLLATERAL:
      return -amount; // Money removed from platform balance

    case TxType.PROPOSAL_COLLATERAL_LOCK:
      return -amount; // Money locked for proposal

    case TxType.PROPOSAL_COLLATERAL_UNLOCK:
    case TxType.PROPOSAL_COLLATERAL_REFUND:
      return amount; // Money returned from proposal

    case TxType.CANCELATION_FEE:
      if (isReceivingCompensation === undefined) {
        throw new Error(
          "isReceivingCompensation must be specified for CANCELATION_FEE transactions"
        );
      }
      return isReceivingCompensation ? amount : -amount;

    default:
      throw new Error(`Unknown transaction type: ${txType}`);
  }
}
